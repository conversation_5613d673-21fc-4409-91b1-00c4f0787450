package com.midas.crm.entity.DTO.calendar;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CalendarDTO {
    private String titulo;
    private String descripcion;
    private String color;
    private LocalDate fechaInicio;
    private LocalTime horaInicio;
    private LocalDate fechaFinal;
    private LocalTime horaFinal;
    private Long userCreateId;
    private Long userUpdateId;
    private Long userDeleteId;
}
