package com.midas.crm.entity.DTO.role;

import com.midas.crm.entity.Role;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RoleDTO {
    private Role role;
    private String name;
    private String description;
    private int userCount;

    public RoleDTO(Role role) {
        this.role = role;
        this.name = role.name();
        this.description = getDescriptionForRole(role);
    }

    private String getDescriptionForRole(Role role) {
        switch (role) {
            case ADMIN:
                return "Administrador del sistema con acceso completo";
            case ASESOR:
                return "Asesor de ventas";
            case BACKOFFICE:
                return "Personal de back office";
            case BACKOFFICETRAMITADOR:
                return "Tramitador de back office";
            case BACKOFFICESEGUIMIENTO:
                return "Seguimiento de back office";
            case COORDINADOR:
                return "Coordinador de equipo";
            case AUDITOR:
                return "Auditor del sistema";
            case PROGRAMADOR:
                return "Programador del sistema";
            case GERENCIA:
                return "Personal de gerencia";
            case PSICOLOGO:
                return "Psicólogo";
            default:
                return "Rol del sistema";
        }
    }
}
