package com.midas.crm.entity.DTO.role;

import com.midas.crm.entity.Role;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RoleRouteDTO {
    private Long id;
    private Role role;
    private Long routeId;
    private String routePath;
    private String routeName;
    private Boolean canAccess;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
