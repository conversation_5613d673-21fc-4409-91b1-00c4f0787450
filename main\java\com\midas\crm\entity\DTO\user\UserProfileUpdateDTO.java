package com.midas.crm.entity.DTO.user;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * DTO para actualización de perfil de usuario
 * Solo permite actualizar campos específicos (no username, password, role)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserProfileUpdateDTO {

    @NotBlank(message = "El nombre es requerido")
    @Size(min = 2, max = 50, message = "El nombre debe tener entre 2 y 50 caracteres")
    private String nombre;

    @NotBlank(message = "El apellido es requerido")
    @Size(min = 2, max = 50, message = "El apellido debe tener entre 2 y 50 caracteres")
    private String apellido;

    @Size(max = 15, message = "El teléfono no debe superar los 15 caracteres")
    private String telefono;

    @Email(message = "El email debe tener un formato válido")
    @Size(max = 50, message = "El email no debe superar los 50 caracteres")
    private String email;

    @Size(max = 1200, message = "La URL de la imagen no debe superar los 1200 caracteres")
    private String picture;
}
