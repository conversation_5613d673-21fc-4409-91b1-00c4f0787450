package com.midas.crm.entity.DTO;

import com.midas.crm.entity.IndependentTranscription.TranscriptionStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO para transferir datos de transcripciones independientes
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IndependentTranscriptionDTO {

    private Long id;
    private String fileName;
    private String originalFileName;
    private String transcriptionText;
    private String audioFileUrl;
    private String transcriptionFileUrl;
    private Integer duration;
    private Long fileSize;
    private String mimeType;
    private String whisperModel;
    private Double confidence;
    private String language;
    private Long processingTime;
    private TranscriptionStatus status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String createdBy;
    private List<String> tags;
    private String notes;

    // Campos adicionales para la vista
    private String formattedDuration;
    private String formattedFileSize;
    private Double confidencePercentage;
    private String statusDisplayName;

    /**
     * Constructor para crear DTO desde entidad
     */
    public IndependentTranscriptionDTO(Long id, String fileName, String originalFileName,
                                       TranscriptionStatus status, LocalDateTime createdAt,
                                       Integer duration, Long fileSize, Double confidence, String language) {
        this.id = id;
        this.fileName = fileName;
        this.originalFileName = originalFileName;
        this.status = status;
        this.createdAt = createdAt;
        this.duration = duration;
        this.fileSize = fileSize;
        this.confidence = confidence;
        this.language = language;
    }

    /**
     * Obtiene el porcentaje de confianza
     */
    public Double getConfidencePercentage() {
        return confidence != null ? confidence * 100.0 : null;
    }

    /**
     * Obtiene el nombre de estado para mostrar
     */
    public String getStatusDisplayName() {
        if (status == null) return "";

        switch (status) {
            case PENDING: return "Pendiente";
            case PROCESSING: return "Procesando";
            case COMPLETED: return "Completado";
            case FAILED: return "Fallido";
            case CANCELLED: return "Cancelado";
            default: return status.name();
        }
    }

    /**
     * Formatea la duración en formato legible
     */
    public String getFormattedDuration() {
        if (duration == null || duration <= 0) {
            return "0:00";
        }

        int hours = duration / 3600;
        int minutes = (duration % 3600) / 60;
        int seconds = duration % 60;

        if (hours > 0) {
            return String.format("%d:%02d:%02d", hours, minutes, seconds);
        }
        return String.format("%d:%02d", minutes, seconds);
    }

    /**
     * Formatea el tamaño del archivo
     */
    public String getFormattedFileSize() {
        if (fileSize == null || fileSize <= 0) {
            return "0 Bytes";
        }

        String[] units = {"Bytes", "KB", "MB", "GB"};
        int unitIndex = 0;
        double size = fileSize.doubleValue();

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return String.format("%.2f %s", size, units[unitIndex]);
    }
}

/**
 * DTO para crear una nueva transcripción independiente
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
class CreateIndependentTranscriptionDTO {
    private String fileName;
    private String whisperModel;
    private String targetLanguage;
    private List<String> tags;
    private String notes;
}

/**
 * DTO para actualizar una transcripción independiente
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
class UpdateIndependentTranscriptionDTO {
    private String fileName;
    private List<String> tags;
    private String notes;
    private TranscriptionStatus status;
}

/**
 * DTO para filtros de búsqueda
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
class IndependentTranscriptionFiltersDTO {
    private List<TranscriptionStatus> status;
    private String dateFrom;
    private String dateTo;
    private String fileName;
    private String language;
    private List<String> tags;
    private String createdBy;
    private Integer minDuration;
    private Integer maxDuration;
    private Double minConfidence;
    private Double maxConfidence;
}

/**
 * DTO para estadísticas
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
class IndependentTranscriptionStatisticsDTO {
    private Long total;
    private Long totalDuration;
    private Double averageConfidence;
    private Long totalFileSize;
    private java.util.Map<TranscriptionStatus, Long> byStatus;
    private java.util.Map<String, Long> byLanguage;
    private java.util.Map<String, Long> byWhisperModel;
    private java.util.List<TagStatisticDTO> popularTags;
}

/**
 * DTO para estadísticas de etiquetas
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
class TagStatisticDTO {
    private String tag;
    private Long count;
}

/**
 * DTO para exportación a Word
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
class ExportToWordDTO {
    private List<Long> transcriptionIds;
    private Boolean includeMetadata = true;
    private Boolean includeTimestamps = false;
    private String documentTitle = "Transcripciones Independientes";
    private String documentAuthor = "Sistema CRM Midas";
}

/**
 * DTO para respuesta de descarga
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
class DownloadResponseDTO {
    private String url;
    private String fileName;
    private String contentType;
    private Long fileSize;
}

/**
 * DTO para metadatos de transcripción
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
class TranscriptionMetadataDTO {
    private String uploadedFrom;
    private ClientInfoDTO clientInfo;
    private AudioInfoDTO audioInfo;
    private DriveInfoDTO driveInfo;
}

/**
 * DTO para información del cliente
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
class ClientInfoDTO {
    private String userAgent;
    private String ipAddress;
}

/**
 * DTO para información de audio
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
class AudioInfoDTO {
    private Integer channels;
    private Integer sampleRate;
    private Integer bitRate;
    private String codec;
}

/**
 * DTO para información de Google Drive
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
class DriveInfoDTO {
    private String folderId;
    private String folderPath;
    private List<String> sharedWith;
}
