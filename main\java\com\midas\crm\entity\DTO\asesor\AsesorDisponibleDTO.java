package com.midas.crm.entity.DTO.asesor;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO reducido para listar asesores sin coordinador.
 * Solo expone id, nombre, apellido, username y sede.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AsesorDisponibleDTO {
    private Long   id;
    private String nombre;
    private String apellido;
    private String username;
    private String sede;   // Nombre de la sede
}
