package com.midas.crm.entity.DTO.asistencia;

import com.midas.crm.entity.Asistencia;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AsistenciaDTO {
    
    private Long id;
    private Long usuarioId;
    private String usuarioNombre;
    private String usuarioApellido;
    private String usuarioUsername;
    private LocalDateTime fechaHoraEntrada;
    private LocalDateTime fechaHoraSalida;
    private String ipEntrada;
    private String ipSalida;
    private String dispositivoEntrada;
    private String dispositivoSalida;
    private String ubicacionEntrada;
    private String ubicacionSalida;
    private Asistencia.TipoActividad tipoActividad;
    private Asistencia.SubtipoActividad subtipoActividad;
    private Integer duracionMinutos;
    private Integer tiempoSesionMinutos;
    private Integer contadorBreakDia;
    private Integer contadorBanoDia;
    private String estado;
    private String observaciones;
    private LocalDateTime fechaCreacion;
    private LocalDateTime fechaActualizacion;
    
    // Campos calculados
    private String tiempoTrabajado; // Duración entre entrada y salida
    private String fechaFormateada; // Fecha en formato legible
    private String horaEntradaFormateada; // Hora de entrada formateada
    private String horaSalidaFormateada; // Hora de salida formateada
}
