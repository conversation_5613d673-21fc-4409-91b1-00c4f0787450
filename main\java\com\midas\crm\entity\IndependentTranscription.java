package com.midas.crm.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * Entidad para transcripciones independientes
 * Permite realizar transcripciones de audio sin dependencia de leads
 */
@Entity
@Table(name = "independent_transcriptions")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IndependentTranscription {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "file_name", nullable = false, length = 255)
    private String fileName;

    @Column(name = "original_file_name", nullable = false, length = 255)
    private String originalFileName;

    @Column(name = "transcription_text", columnDefinition = "LONGTEXT")
    private String transcriptionText;

    @Column(name = "audio_file_url", length = 500)
    private String audioFileUrl; // URL del archivo de audio en Google Drive

    @Column(name = "transcription_file_url", length = 500)
    private String transcriptionFileUrl; // URL del archivo .txt en Google Drive

    @Column(name = "transcription_file_id", length = 100)
    private String transcriptionFileId; // ID del archivo en Google Drive

    @Column(name = "audio_file_id", length = 100)
    private String audioFileId; // ID del archivo de audio en Google Drive

    @Column(name = "duration")
    private Integer duration; // Duración en segundos

    @Column(name = "file_size")
    private Long fileSize; // Tamaño del archivo en bytes

    @Column(name = "mime_type", length = 100)
    private String mimeType; // Tipo MIME del archivo de audio

    @Column(name = "whisper_model", length = 50)
    private String whisperModel; // Modelo de Whisper utilizado

    @Column(name = "confidence")
    private Double confidence; // Nivel de confianza de la transcripción (0.0 - 1.0)

    @Column(name = "language", length = 10)
    private String language; // Idioma detectado/especificado

    @Column(name = "processing_time")
    private Long processingTime; // Tiempo de procesamiento en milisegundos

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private TranscriptionStatus status = TranscriptionStatus.PENDING;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "created_by", length = 100)
    private String createdBy; // Usuario que creó la transcripción

    @Column(name = "tags", length = 1000)
    private String tags; // Etiquetas separadas por comas para organización

    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes; // Notas adicionales del usuario

    // Metadatos adicionales almacenados como JSON
    @Column(name = "metadata", columnDefinition = "JSON")
    private String metadata;

    // Información de la carpeta en Google Drive
    @Column(name = "drive_folder_id", length = 100)
    private String driveFolderId;

    @Column(name = "drive_folder_path", length = 500)
    private String driveFolderPath;

    // Información del cliente (opcional)
    @Column(name = "uploaded_from", length = 100)
    private String uploadedFrom; // Origen del archivo (web, mobile, etc.)

    @Column(name = "client_ip", length = 45)
    private String clientIp;

    @Column(name = "user_agent", length = 500)
    private String userAgent;

    // Información de audio adicional
    @Column(name = "audio_channels")
    private Integer audioChannels;

    @Column(name = "sample_rate")
    private Integer sampleRate;

    @Column(name = "bit_rate")
    private Integer bitRate;

    @Column(name = "codec", length = 50)
    private String codec;

    /**
     * Enum para el estado de la transcripción
     */
    public enum TranscriptionStatus {
        PENDING,    // Pendiente de procesamiento
        PROCESSING, // En proceso de transcripción
        COMPLETED,  // Transcripción completada
        FAILED,     // Transcripción fallida
        CANCELLED   // Transcripción cancelada
    }

    /**
     * Constructor para crear una nueva transcripción
     */
    public IndependentTranscription(String fileName, String originalFileName, String mimeType, Long fileSize) {
        this.fileName = fileName;
        this.originalFileName = originalFileName;
        this.mimeType = mimeType;
        this.fileSize = fileSize;
        this.status = TranscriptionStatus.PENDING;
    }

    /**
     * Verifica si la transcripción está completada
     */
    public boolean isCompleted() {
        return TranscriptionStatus.COMPLETED.equals(this.status);
    }

    /**
     * Verifica si la transcripción está en proceso
     */
    public boolean isProcessing() {
        return TranscriptionStatus.PROCESSING.equals(this.status);
    }

    /**
     * Verifica si la transcripción ha fallado
     */
    public boolean isFailed() {
        return TranscriptionStatus.FAILED.equals(this.status);
    }

    /**
     * Verifica si la transcripción fue cancelada
     */
    public boolean isCancelled() {
        return TranscriptionStatus.CANCELLED.equals(this.status);
    }

    /**
     * Marca la transcripción como completada
     */
    public void markAsCompleted() {
        this.status = TranscriptionStatus.COMPLETED;
    }

    /**
     * Marca la transcripción como fallida
     */
    public void markAsFailed() {
        this.status = TranscriptionStatus.FAILED;
    }

    /**
     * Marca la transcripción como en proceso
     */
    public void markAsProcessing() {
        this.status = TranscriptionStatus.PROCESSING;
    }

    /**
     * Marca la transcripción como cancelada
     */
    public void markAsCancelled() {
        this.status = TranscriptionStatus.CANCELLED;
    }

    /**
     * Obtiene las etiquetas como array
     */
    public String[] getTagsArray() {
        if (tags == null || tags.trim().isEmpty()) {
            return new String[0];
        }
        return tags.split(",");
    }

    /**
     * Establece las etiquetas desde un array
     */
    public void setTagsFromArray(String[] tagsArray) {
        if (tagsArray == null || tagsArray.length == 0) {
            this.tags = null;
        } else {
            this.tags = String.join(",", tagsArray);
        }
    }

    /**
     * Calcula el porcentaje de confianza
     */
    public double getConfidencePercentage() {
        return confidence != null ? confidence * 100.0 : 0.0;
    }

    /**
     * Formatea la duración en formato legible
     */
    public String getFormattedDuration() {
        if (duration == null || duration <= 0) {
            return "0:00";
        }

        int hours = duration / 3600;
        int minutes = (duration % 3600) / 60;
        int seconds = duration % 60;

        if (hours > 0) {
            return String.format("%d:%02d:%02d", hours, minutes, seconds);
        }
        return String.format("%d:%02d", minutes, seconds);
    }

    /**
     * Formatea el tamaño del archivo
     */
    public String getFormattedFileSize() {
        if (fileSize == null || fileSize <= 0) {
            return "0 Bytes";
        }

        String[] units = {"Bytes", "KB", "MB", "GB"};
        int unitIndex = 0;
        double size = fileSize.doubleValue();

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return String.format("%.2f %s", size, units[unitIndex]);
    }
}
