package com.midas.crm.entity.DTO.manual;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * DTO para las respuestas de la API. Define la estructura JSON que verá el cliente.
 * Es ligero y no expone detalles de la base de datos.
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ManualResponseDto {

    private int id;
    private String nombre;
    private String tipo; // Texto descriptivo: "Informativo", "Tarifario", etc.
    private String archivo;
    private boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String sedeNombre;

}