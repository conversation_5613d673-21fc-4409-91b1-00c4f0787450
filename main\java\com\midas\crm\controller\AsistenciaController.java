package com.midas.crm.controller;

import com.midas.crm.entity.DTO.asistencia.AsistenciaCreateDTO;
import com.midas.crm.entity.DTO.asistencia.AsistenciaDTO;
import com.midas.crm.entity.DTO.asistencia.AsistenciaFilterDTO;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.User;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.security.UserPrincipal;
import com.midas.crm.security.jwt.JwtUtil;
import com.midas.crm.service.AsistenciaService;
import com.midas.crm.service.UserService;
import com.midas.crm.utils.GenericResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.Optional;

@RestController
@RequestMapping("${api.route.asistencias}")
@RequiredArgsConstructor
@Slf4j
public class AsistenciaController {

    private final AsistenciaService asistenciaService;
    private final UserRepository userRepository;
    private final JwtUtil jwtUtil;

    /**
     * Registra una nueva asistencia
     */
    @PostMapping
    public ResponseEntity<GenericResponse<AsistenciaDTO>> registrarAsistencia(
            @Valid @RequestBody AsistenciaCreateDTO dto,
            HttpServletRequest request) {

        // Obtener información adicional de la request
        String ip = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");

        if (dto.getIpEntrada() == null) {
            dto.setIpEntrada(ip);
        }
        if (dto.getDispositivoEntrada() == null) {
            dto.setDispositivoEntrada(userAgent);
        }

        GenericResponse<AsistenciaDTO> response = asistenciaService.registrarAsistencia(dto);
        return ResponseEntity.ok(response);
    }

    /**
     * Registra asistencia automática (usado internamente por el login)
     */
    @PostMapping("/automatica")
    public ResponseEntity<GenericResponse<AsistenciaDTO>> registrarAsistenciaAutomatica(
            @RequestParam Long usuarioId,
            HttpServletRequest request) {

        String ip = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");
        String ubicacion = "Sistema";

        GenericResponse<AsistenciaDTO> response = asistenciaService.registrarAsistenciaAutomatica(
                usuarioId, ip, userAgent, ubicacion);
        return ResponseEntity.ok(response);
    }

    /**
     * Obtiene asistencias con filtros y paginación
     */
    @PostMapping("/filtros")
    public ResponseEntity<GenericResponse<Page<AsistenciaDTO>>> obtenerAsistenciasConFiltros(
            @RequestBody AsistenciaFilterDTO filtros) {

        GenericResponse<Page<AsistenciaDTO>> response = asistenciaService.obtenerAsistenciasConFiltros(filtros);
        return ResponseEntity.ok(response);
    }

    /**
     * Obtiene asistencias por fecha específica
     */
    @GetMapping("/fecha")
    public ResponseEntity<GenericResponse<List<AsistenciaDTO>>> obtenerAsistenciasPorFecha(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fecha) {

        try {
            GenericResponse<List<AsistenciaDTO>> response = asistenciaService.obtenerAsistenciasPorFecha(fecha);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error al obtener asistencias por fecha: {}", e.getMessage(), e);
            GenericResponse<List<AsistenciaDTO>> errorResponse = new GenericResponse<>(0, "Error al obtener asistencias: " + e.getMessage(), null);
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * Obtiene asistencias por fecha específica (endpoint original)
     */
    @GetMapping("/fecha/{fecha}")
    public ResponseEntity<GenericResponse<List<AsistenciaDTO>>> obtenerAsistenciasPorFechaPath(
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fecha) {

        GenericResponse<List<AsistenciaDTO>> response = asistenciaService.obtenerAsistenciasPorFecha(fecha);
        return ResponseEntity.ok(response);
    }

    /**
     * Obtiene asistencias de un usuario específico
     */
    @GetMapping("/usuario/{usuarioId}")
    public ResponseEntity<GenericResponse<List<AsistenciaDTO>>> obtenerAsistenciasPorUsuario(
            @PathVariable Long usuarioId) {

        GenericResponse<List<AsistenciaDTO>> response = asistenciaService.obtenerAsistenciasPorUsuario(usuarioId);
        return ResponseEntity.ok(response);
    }

    /**
     * Obtiene asistencias de un usuario en un rango de fechas
     */
    @GetMapping("/usuario/rango")
    public ResponseEntity<GenericResponse<List<AsistenciaDTO>>> obtenerAsistenciasUsuarioRango(
            @RequestParam Long usuarioId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin) {

        try {
            GenericResponse<List<AsistenciaDTO>> response = asistenciaService.obtenerAsistenciasPorUsuarioYFechas(
                    usuarioId, fechaInicio, fechaFin);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error al obtener asistencias por usuario y rango: {}", e.getMessage(), e);
            GenericResponse<List<AsistenciaDTO>> errorResponse = new GenericResponse<>(0, "Error al obtener asistencias: " + e.getMessage(), null);
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * Obtiene asistencias de un usuario en un rango de fechas (endpoint original)
     */
    @GetMapping("/usuario/{usuarioId}/fechas")
    public ResponseEntity<GenericResponse<List<AsistenciaDTO>>> obtenerAsistenciasPorUsuarioYFechas(
            @PathVariable Long usuarioId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin) {

        GenericResponse<List<AsistenciaDTO>> response = asistenciaService.obtenerAsistenciasPorUsuarioYFechas(
                usuarioId, fechaInicio, fechaFin);
        return ResponseEntity.ok(response);
    }

    /**
     * Obtiene estadísticas de asistencias por fecha
     */
    @GetMapping("/estadisticas")
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerEstadisticasPorFecha(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin) {

        GenericResponse<Map<String, Object>> response = asistenciaService.obtenerEstadisticasPorFecha(fechaInicio, fechaFin);
        return ResponseEntity.ok(response);
    }

    /**
     * Registra salida de un usuario
     */
    @PostMapping("/salida")
    public ResponseEntity<GenericResponse<AsistenciaDTO>> registrarSalida(
            @RequestParam Long usuarioId,
            HttpServletRequest request) {

        String ip = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");
        String ubicacion = "Sistema";

        GenericResponse<AsistenciaDTO> response = asistenciaService.registrarSalida(usuarioId, ip, userAgent, ubicacion);
        return ResponseEntity.ok(response);
    }

    /**
     * Obtiene una asistencia por ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<GenericResponse<AsistenciaDTO>> obtenerAsistenciaPorId(@PathVariable Long id) {
        GenericResponse<AsistenciaDTO> response = asistenciaService.obtenerAsistenciaPorId(id);
        return ResponseEntity.ok(response);
    }

    /**
     * Verifica si un usuario ya registró asistencia hoy
     */
    @GetMapping("/verificar/{usuarioId}")
    public ResponseEntity<GenericResponse<Boolean>> verificarAsistenciaHoy(@PathVariable Long usuarioId) {
        boolean yaRegistro = asistenciaService.yaRegistroAsistenciaHoy(usuarioId);
        GenericResponse<Boolean> response = new GenericResponse<>(1, "Verificación completada", yaRegistro);
        return ResponseEntity.ok(response);
    }

    /**
     * Verifica si un usuario ya registró asistencia hoy (endpoint alternativo)
     */
    @GetMapping("/verificar-hoy/{usuarioId}")
    public ResponseEntity<GenericResponse<Boolean>> verificarAsistenciaHoyAlt(@PathVariable Long usuarioId) {
        try {
            boolean yaRegistro = asistenciaService.yaRegistroAsistenciaHoy(usuarioId);
            GenericResponse<Boolean> response = new GenericResponse<>(1, "Verificación completada", yaRegistro);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error al verificar asistencia del día: {}", e.getMessage(), e);
            GenericResponse<Boolean> errorResponse = new GenericResponse<>(0, "Error al verificar asistencia: " + e.getMessage(), false);
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * Inicia un break para el usuario
     */
    @PostMapping("/break/iniciar")
    public ResponseEntity<GenericResponse<AsistenciaDTO>> iniciarBreak(
            @RequestParam Long usuarioId,
            HttpServletRequest request) {

        String ip = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");
        String ubicacion = "Sistema";

        GenericResponse<AsistenciaDTO> response = asistenciaService.iniciarBreak(usuarioId, ip, userAgent, ubicacion);
        return ResponseEntity.ok(response);
    }

    /**
     * Finaliza un break para el usuario
     */
    @PostMapping("/break/finalizar")
    public ResponseEntity<GenericResponse<AsistenciaDTO>> finalizarBreak(
            @RequestParam Long usuarioId,
            HttpServletRequest request) {

        try {
            String ip = getClientIpAddress(request);
            String userAgent = request.getHeader("User-Agent");
            String ubicacion = "Sistema";

            GenericResponse<AsistenciaDTO> response = asistenciaService.finalizarBreak(usuarioId, ip, userAgent, ubicacion);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error al finalizar break para usuario {}: {}", usuarioId, e.getMessage(), e);
            GenericResponse<AsistenciaDTO> errorResponse = new GenericResponse<>(0, "Error al finalizar break: " + e.getMessage(), null);
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * Inicia ida al baño para el usuario
     */
    @PostMapping("/bano/iniciar")
    public ResponseEntity<GenericResponse<AsistenciaDTO>> iniciarBano(
            @RequestParam Long usuarioId,
            HttpServletRequest request) {

        String ip = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");
        String ubicacion = "Sistema";

        GenericResponse<AsistenciaDTO> response = asistenciaService.iniciarBano(usuarioId, ip, userAgent, ubicacion);
        return ResponseEntity.ok(response);
    }

    /**
     * Finaliza ida al baño para el usuario
     */
    @PostMapping("/bano/finalizar")
    public ResponseEntity<GenericResponse<AsistenciaDTO>> finalizarBano(
            @RequestParam Long usuarioId,
            HttpServletRequest request) {

        try {
            String ip = getClientIpAddress(request);
            String userAgent = request.getHeader("User-Agent");
            String ubicacion = "Sistema";

            GenericResponse<AsistenciaDTO> response = asistenciaService.finalizarBano(usuarioId, ip, userAgent, ubicacion);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error al finalizar baño para usuario {}: {}", usuarioId, e.getMessage(), e);
            GenericResponse<AsistenciaDTO> errorResponse = new GenericResponse<>(0, "Error al finalizar baño: " + e.getMessage(), null);
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * Inicia sesión CRM para el usuario
     */
    @PostMapping("/sesion-crm/iniciar")
    public ResponseEntity<GenericResponse<AsistenciaDTO>> iniciarSesionCrm(
            @RequestParam Long usuarioId,
            HttpServletRequest request) {

        String ip = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");
        String ubicacion = "Sistema";

        GenericResponse<AsistenciaDTO> response = asistenciaService.iniciarSesionCrm(usuarioId, ip, userAgent, ubicacion);
        return ResponseEntity.ok(response);
    }

    /**
     * Finaliza sesión CRM para el usuario
     */
    @PostMapping("/sesion-crm/finalizar")
    public ResponseEntity<GenericResponse<AsistenciaDTO>> finalizarSesionCrm(
            @RequestParam Long usuarioId,
            HttpServletRequest request) {

        String ip = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");
        String ubicacion = "Sistema";

        GenericResponse<AsistenciaDTO> response = asistenciaService.finalizarSesionCrm(usuarioId, ip, userAgent, ubicacion);
        return ResponseEntity.ok(response);
    }

    /**
     * Verifica si el usuario puede tomar break
     */
    @GetMapping("/break/puede-tomar/{usuarioId}")
    public ResponseEntity<GenericResponse<Boolean>> puedeTomarBreak(@PathVariable Long usuarioId) {
        try {
            boolean puede = asistenciaService.puedeTomarBreak(usuarioId);
            GenericResponse<Boolean> response = new GenericResponse<>(1, "Verificación completada", puede);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error al verificar si puede tomar break: {}", e.getMessage(), e);
            GenericResponse<Boolean> errorResponse = new GenericResponse<>(0, "Error en verificación: " + e.getMessage(), false);
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * Verifica si el usuario puede ir al baño
     */
    @GetMapping("/bano/puede-ir/{usuarioId}")
    public ResponseEntity<GenericResponse<Boolean>> puedeIrAlBano(@PathVariable Long usuarioId) {
        try {
            boolean puede = asistenciaService.puedeIrAlBano(usuarioId);
            GenericResponse<Boolean> response = new GenericResponse<>(1, "Verificación completada", puede);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error al verificar si puede ir al baño: {}", e.getMessage(), e);
            GenericResponse<Boolean> errorResponse = new GenericResponse<>(0, "Error en verificación: " + e.getMessage(), false);
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * Endpoint para verificar actividades activas
     */
    @GetMapping("/actividad-activa/{usuarioId}")
    public ResponseEntity<GenericResponse<Map<String, Object>>> verificarActividadActiva(@PathVariable Long usuarioId) {
        try {
            GenericResponse<Map<String, Object>> response = asistenciaService.obtenerEstadoActividades(usuarioId);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error al obtener estado de actividades: {}", e.getMessage(), e);
            GenericResponse<Map<String, Object>> errorResponse = new GenericResponse<>(0, "Error al obtener estado: " + e.getMessage(), null);
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * Verifica si un usuario ya marcó salida hoy
     */
    @GetMapping("/verificar-salida/{usuarioId}")
    public ResponseEntity<GenericResponse<Boolean>> verificarSalidaHoy(@PathVariable Long usuarioId) {
        try {
            boolean yaMarcoSalida = asistenciaService.yaMarcoSalidaHoy(usuarioId);
            GenericResponse<Boolean> response = new GenericResponse<>(1, "Verificación completada", yaMarcoSalida);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error al verificar salida del día: {}", e.getMessage(), e);
            GenericResponse<Boolean> errorResponse = new GenericResponse<>(0, "Error al verificar salida: " + e.getMessage(), false);
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * Endpoint para auto-finalizar actividades por tiempo límite
     */
    @PostMapping("/auto-finalizar/{usuarioId}")
    public ResponseEntity<GenericResponse<List<AsistenciaDTO>>> autoFinalizarActividades(@PathVariable Long usuarioId) {
        try {
            GenericResponse<List<AsistenciaDTO>> response = asistenciaService.autoFinalizarActividadesPorTiempo(usuarioId);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error en auto-finalización: {}", e.getMessage(), e);
            GenericResponse<List<AsistenciaDTO>> errorResponse = new GenericResponse<>(0, "Error en auto-finalización: " + e.getMessage(), null);
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * 🔥 CORREGIDO: Filtros admin con transaccional y validaciones
     */
    @PostMapping("/admin/filtros")
    @Transactional(readOnly = true)
    public ResponseEntity<GenericResponse<Page<AsistenciaDTO>>> obtenerAsistenciasAdmin(
            @Valid @RequestBody AsistenciaFilterDTO filtros,
            Authentication authentication) {

        try {
            log.info("🔥 Recibida solicitud admin/filtros con Authentication: {}", authentication != null ? authentication.getName() : "null");

            if (authentication == null || !authentication.isAuthenticated()) {
                log.error("❌ Usuario no autenticado para filtros admin");
                return ResponseEntity.status(401).body(
                        new GenericResponse<>(0, "Usuario no autenticado", null)
                );
            }

            String username = authentication.getName();
            log.info("✅ Usuario autenticado: {}", username);

            // 🔥 USAR CONSULTA OPTIMIZADA CON JOIN FETCH
            User admin = userRepository.findByIdWithSedeAndCoordinador(
                    userRepository.findByUsername(username)
                            .orElseThrow(() -> new RuntimeException("Usuario no encontrado"))
                            .getId()
            ).orElse(null);

            if (admin == null) {
                log.error("❌ Usuario {} no encontrado en BD", username);
                return ResponseEntity.status(404).body(
                        new GenericResponse<>(0, "Usuario no encontrado", null)
                );
            }

            if (!Role.ADMIN.equals(admin.getRole())) {
                log.error("❌ Usuario {} no es ADMIN, rol actual: {}", username, admin.getRole());
                return ResponseEntity.status(403).body(
                        new GenericResponse<>(0, "No tiene permisos de administrador", null)
                );
            }

            // Aplicar filtro por sede si el admin tiene una sede asignada
            if (admin.getSede() != null) {
                log.info("🏢 Aplicando filtro por sede: {} (ID: {})", admin.getSede().getNombre(), admin.getSede().getId());
                filtros.setSedeId(admin.getSede().getId());
            } else {
                log.warn("⚠️ Admin {} no tiene sede asignada", username);
            }

            // 🔥 VALIDAR Y CONFIGURAR FILTROS POR DEFECTO
            if (filtros.getPage() == null) filtros.setPage(0);
            if (filtros.getSize() == null) filtros.setSize(50); // Reducir tamaño de página para mejor performance
            if (filtros.getSortBy() == null) filtros.setSortBy("fechaHoraEntrada");
            if (filtros.getSortDirection() == null) filtros.setSortDirection("DESC");
            if (filtros.getEstado() == null) filtros.setEstado("A");

            log.info("📊 Obteniendo asistencias con filtros: {}", filtros);
            GenericResponse<Page<AsistenciaDTO>> response = asistenciaService.obtenerAsistenciasConFiltros(filtros);

            log.info("✅ Respuesta exitosa: {} registros encontrados",
                    response.getData() != null ? response.getData().getTotalElements() : 0);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("❌ Error al obtener asistencias admin: {}", e.getMessage(), e);
            GenericResponse<Page<AsistenciaDTO>> errorResponse = new GenericResponse<>(0, "Error al obtener asistencias: " + e.getMessage(), null);
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * 🔥 PAGINADO: Usuarios sede con paginación y búsqueda
     */
    @GetMapping("/admin/usuarios-sede")
    @Transactional(readOnly = true, timeout = 8)
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerUsuariosSedePaginado(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String search,
            Authentication authentication) {

        try {
            long startTime = System.currentTimeMillis();
            log.info("🔥 USUARIOS-SEDE ULTRA-OPTIMIZADO iniciado");

            // Validaciones rápidas
            if (authentication == null || !authentication.isAuthenticated()) {
                return ResponseEntity.status(401).body(
                        new GenericResponse<>(0, "Usuario no autenticado", null)
                );
            }

            String username = authentication.getName();
            log.debug("✅ Usuario: {}", username);

            // 🔥 CONSULTA OPTIMIZADA: Usar nueva consulta con índices
            User admin = userRepository.findByUsernameWithSedeAndCoordinador(username).orElse(null);

            if (admin == null || !Role.ADMIN.equals(admin.getRole()) || admin.getSede() == null) {
                log.warn("❌ Admin inválido o sin sede: {}", username);
                return ResponseEntity.status(403).body(
                        new GenericResponse<>(0, "Sin permisos o sede no asignada", null)
                );
            }

            log.info("🏢 Obteniendo usuarios de sede: {} (ID: {})",
                    admin.getSede().getNombre(), admin.getSede().getId());

            // 🔥 LLAMADA PAGINADA
            GenericResponse<Map<String, Object>> response =
                    asistenciaService.obtenerUsuariosDeSedePaginado(admin.getSede().getId(), page, size, search);

            long totalTime = System.currentTimeMillis() - startTime;
            log.info("✅ USUARIOS-SEDE completado en {}ms - {} usuarios",
                    totalTime, response.getData() != null ? response.getData().size() : 0);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("❌ Error en usuarios-sede: {}", e.getMessage(), e);
            return ResponseEntity.ok(new GenericResponse<>(0, "Error: " + e.getMessage(), null));
        }
    }


    /**
     * 🔥 NUEVO: Obtiene estadísticas detalladas por usuario
     */
    @GetMapping("/admin/estadisticas-usuario")
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerEstadisticasUsuario(
            @RequestParam Long usuarioId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin,
            Authentication authentication) {

        try {
            if (authentication == null || !authentication.isAuthenticated()) {
                return ResponseEntity.status(401).body(
                        new GenericResponse<>(0, "Usuario no autenticado", null)
                );
            }

            String username = authentication.getName();
            User admin = userRepository.findByUsername(username).orElse(null);

            if (admin == null || !Role.ADMIN.equals(admin.getRole())) {
                return ResponseEntity.status(403).body(
                        new GenericResponse<>(0, "No tiene permisos de administrador", null)
                );
            }

            GenericResponse<List<AsistenciaDTO>> response = asistenciaService.obtenerAsistenciasPorUsuarioYFechas(
                    usuarioId, fechaInicio, fechaFin);

            Map<String, Object> estadisticas = new HashMap<>();
            estadisticas.put("asistencias", response.getData());
            estadisticas.put("fechaInicio", fechaInicio);
            estadisticas.put("fechaFin", fechaFin);

            return ResponseEntity.ok(new GenericResponse<>(1, "Estadísticas obtenidas", estadisticas));

        } catch (Exception e) {
            log.error("Error al obtener estadísticas de usuario: {}", e.getMessage(), e);
            GenericResponse<Map<String, Object>> errorResponse = new GenericResponse<>(0, "Error: " + e.getMessage(), null);
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * 🔥 NUEVO: Obtiene resumen diario
     */
    @GetMapping("/admin/resumen-diario")
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerResumenDiario(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fecha,
            Authentication authentication) {

        try {
            if (authentication == null || !authentication.isAuthenticated()) {
                return ResponseEntity.status(401).body(
                        new GenericResponse<>(0, "Usuario no autenticado", null)
                );
            }

            String username = authentication.getName();
            User admin = userRepository.findByUsername(username).orElse(null);

            if (admin == null || !Role.ADMIN.equals(admin.getRole())) {
                return ResponseEntity.status(403).body(
                        new GenericResponse<>(0, "No tiene permisos de administrador", null)
                );
            }

            if (admin.getSede() == null) {
                return ResponseEntity.status(400).body(
                        new GenericResponse<>(0, "Admin sin sede asignada", null)
                );
            }

            GenericResponse<Map<String, Object>> response = asistenciaService.obtenerDashboardSede(
                    admin.getSede().getId(), fecha);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error al obtener resumen diario: {}", e.getMessage(), e);
            GenericResponse<Map<String, Object>> errorResponse = new GenericResponse<>(0, "Error: " + e.getMessage(), null);
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * 🔥 NUEVO: Obtiene alertas de asistencia
     */
    @GetMapping("/admin/alertas")
    public ResponseEntity<GenericResponse<List<Map<String, Object>>>> obtenerAlertas(
            Authentication authentication) {

        try {
            if (authentication == null || !authentication.isAuthenticated()) {
                return ResponseEntity.status(401).body(
                        new GenericResponse<>(0, "Usuario no autenticado", null)
                );
            }

            String username = authentication.getName();
            User admin = userRepository.findByUsername(username).orElse(null);

            if (admin == null || !Role.ADMIN.equals(admin.getRole())) {
                return ResponseEntity.status(403).body(
                        new GenericResponse<>(0, "No tiene permisos de administrador", null)
                );
            }

            if (admin.getSede() == null) {
                return ResponseEntity.status(400).body(
                        new GenericResponse<>(0, "Admin sin sede asignada", null)
                );
            }

            // Por ahora retornamos lista vacía - se puede implementar lógica específica de alertas
            List<Map<String, Object>> alertas = new ArrayList<>();

            return ResponseEntity.ok(new GenericResponse<>(1, "Alertas obtenidas", alertas));

        } catch (Exception e) {
            log.error("Error al obtener alertas: {}", e.getMessage(), e);
            GenericResponse<List<Map<String, Object>>> errorResponse = new GenericResponse<>(0, "Error: " + e.getMessage(), null);
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * 🔥 NUEVO: Fuerza finalización de actividad
     */
    @PostMapping("/admin/forzar-finalizacion")
    public ResponseEntity<GenericResponse<AsistenciaDTO>> forzarFinalizacionActividad(
            @RequestBody Map<String, Object> body,
            Authentication authentication) {

        try {
            if (authentication == null || !authentication.isAuthenticated()) {
                return ResponseEntity.status(401).body(
                        new GenericResponse<>(0, "Usuario no autenticado", null)
                );
            }

            String username = authentication.getName();
            User admin = userRepository.findByUsername(username).orElse(null);

            if (admin == null || !Role.ADMIN.equals(admin.getRole())) {
                return ResponseEntity.status(403).body(
                        new GenericResponse<>(0, "No tiene permisos de administrador", null)
                );
            }

            Long usuarioId = Long.valueOf(body.get("usuarioId").toString());
            String tipoActividad = body.get("tipoActividad").toString();

            GenericResponse<AsistenciaDTO> response;

            if ("BREAK".equals(tipoActividad)) {
                response = asistenciaService.finalizarBreak(usuarioId, "Admin", "Finalización forzada", "Sistema");
            } else if ("BANO".equals(tipoActividad)) {
                response = asistenciaService.finalizarBano(usuarioId, "Admin", "Finalización forzada", "Sistema");
            } else {
                return ResponseEntity.badRequest().body(
                        new GenericResponse<>(0, "Tipo de actividad no válido", null)
                );
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error al forzar finalización: {}", e.getMessage(), e);
            GenericResponse<AsistenciaDTO> errorResponse = new GenericResponse<>(0, "Error: " + e.getMessage(), null);
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * 🔥 NUEVO: Verificar estado del servicio
     */
    @GetMapping("/admin/estado")
    public ResponseEntity<GenericResponse<Map<String, Object>>> verificarEstado(
            Authentication authentication) {

        try {
            Map<String, Object> estado = new HashMap<>();
            estado.put("servidor", "activo");
            estado.put("timestamp", LocalDateTime.now());
            estado.put("version", "1.0.0");

            if (authentication != null && authentication.isAuthenticated()) {
                estado.put("autenticado", true);
                estado.put("usuario", authentication.getName());
            } else {
                estado.put("autenticado", false);
            }

            return ResponseEntity.ok(new GenericResponse<>(1, "Estado del servicio", estado));

        } catch (Exception e) {
            log.error("Error al verificar estado: {}", e.getMessage(), e);
            Map<String, Object> estadoError = new HashMap<>();
            estadoError.put("servidor", "error");
            estadoError.put("mensaje", e.getMessage());
            return ResponseEntity.ok(new GenericResponse<>(0, "Error en el servicio", estadoError));
        }
    }

    /**
     * Obtiene la dirección IP real del cliente
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedForHeader = request.getHeader("X-Forwarded-For");
        if (xForwardedForHeader == null) {
            return request.getRemoteAddr();
        } else {
            return xForwardedForHeader.split(",")[0];
        }
    }

    /**
     * 🔥 MÉTODO MEJORADO: Extraer userId usando múltiples estrategias
     */
    private Long extractUserIdFromToken(HttpServletRequest request) {
        try {
            // Estrategia 1: Usar Spring Security Context
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

            if (authentication != null && authentication.getPrincipal() instanceof UserPrincipal) {
                UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
                log.debug("✅ UserId extraído del SecurityContext (UserPrincipal): {}", userPrincipal.getId());
                return userPrincipal.getId();
            }

            if (authentication != null && authentication.getPrincipal() instanceof UserDetails) {
                UserDetails userDetails = (UserDetails) authentication.getPrincipal();
                Optional<User> userOpt = userRepository.findByUsername(userDetails.getUsername());
                if (userOpt.isPresent()) {
                    log.debug("✅ UserId extraído por username del SecurityContext: {}", userOpt.get().getId());
                    return userOpt.get().getId();
                }
            }

            // Estrategia 2: Extraer directamente del token JWT
            String authHeader = request.getHeader("Authorization");
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                String jwt = authHeader.substring(7);

                Long userId = jwtUtil.extractUserId(jwt);
                if (userId != null) {
                    log.debug("✅ UserId extraído directamente del token JWT: {}", userId);
                    return userId;
                }
            }

            log.warn("⚠️ No se pudo extraer userId - Authentication: {}, AuthHeader present: {}",
                    authentication != null ? authentication.getClass().getSimpleName() : "null",
                    authHeader != null);
            return null;

        } catch (Exception e) {
            log.error("❌ Error al extraer userId del token: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 🔥 MÉTODO AUXILIAR: Obtener usuario completo del token
     */
    private User getCurrentUserFromToken(HttpServletRequest request) {
        try {
            Long userId = extractUserIdFromToken(request);
            if (userId == null) {
                return null;
            }
            return userRepository.findById(userId).orElse(null);
        } catch (Exception e) {
            log.error("Error al obtener usuario actual: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 🔥 CORREGIDO: Exportar asistencias a Excel para ADMIN
     */
    @PostMapping("/admin/exportar-excel")
    public ResponseEntity<byte[]> exportarAsistenciasExcel(
            @Valid @RequestBody AsistenciaFilterDTO filtros,
            Authentication authentication) {

        try {
            log.info("🔥 Iniciando exportación Excel con filtros: {}", filtros);

            if (authentication == null || !authentication.isAuthenticated()) {
                log.error("❌ Usuario no autenticado para exportación Excel");
                return ResponseEntity.status(401).build();
            }

            String username = authentication.getName();
            log.info("✅ Usuario autenticado para exportación: {}", username);

            User admin = userRepository.findByIdWithSedeAndCoordinador(
                    userRepository.findByUsername(username)
                            .orElseThrow(() -> new RuntimeException("Usuario no encontrado"))
                            .getId()
            ).orElse(null);

            if (admin == null || !Role.ADMIN.equals(admin.getRole())) {
                log.error("❌ Usuario {} sin permisos de admin para exportación", username);
                return ResponseEntity.status(403).build();
            }

            // Validar que tenga sede asignada
            if (admin.getSede() == null) {
                log.error("❌ Admin {} sin sede asignada", username);
                return ResponseEntity.status(400).build();
            }

            // Forzar filtro por sede del admin
            filtros.setSedeId(admin.getSede().getId());
            log.info("✅ Aplicando filtro de sede: {} (ID: {})", admin.getSede().getNombre(), admin.getSede().getId());

            // Validar filtros antes de exportar
            if (filtros.getFechaInicio() == null || filtros.getFechaFin() == null) {
                log.warn("⚠️ Fechas de filtro faltantes, usando fecha actual");
                LocalDate hoy = LocalDate.now();
                if (filtros.getFechaInicio() == null) {
                    filtros.setFechaInicio(hoy);
                }
                if (filtros.getFechaFin() == null) {
                    filtros.setFechaFin(hoy);
                }
            }

            // Configurar para exportación
            filtros.setPage(0);
            filtros.setSize(10000);
            filtros.setSortBy("fechaHoraEntrada");
            filtros.setSortDirection("DESC");

            log.info("📊 Generando Excel para sede: {}, fechas: {} a {}",
                    admin.getSede().getNombre(),
                    filtros.getFechaInicio(),
                    filtros.getFechaFin());

            byte[] excelBytes = asistenciaService.exportarAsistenciasExcel(filtros);

            if (excelBytes == null || excelBytes.length == 0) {
                log.error("❌ El servicio retornó un archivo vacío");
                return ResponseEntity.status(500).build();
            }

            // Generar nombre de archivo
            String sedeNombre = admin.getSede().getNombre().replaceAll("[^a-zA-Z0-9]", "_");
            String fechaFormateada = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String filename = String.format("Asistencias_%s_%s.xlsx", sedeNombre, fechaFormateada);

            log.info("✅ Excel generado exitosamente: {} bytes, archivo: {}",
                    excelBytes.length, filename);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .header(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                    .header(HttpHeaders.CONTENT_LENGTH, String.valueOf(excelBytes.length))
                    .header("Access-Control-Expose-Headers", "Content-Disposition")
                    .body(excelBytes);

        } catch (Exception e) {
            log.error("❌ Error crítico al exportar Excel: {}", e.getMessage(), e);
            return ResponseEntity.status(500).build();
        }
    }


    /**
     * 🔥 ULTRA-OPTIMIZADO: Dashboard - Ahora debería ser súper rápido
     */
    @GetMapping("/admin/dashboard")
    @Transactional(readOnly = true, timeout = 5)
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerDashboardAdmin(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fecha,
            Authentication authentication) {

        try {
            long startTime = System.currentTimeMillis();
            log.info("🔥 DASHBOARD ULTRA-OPTIMIZADO iniciado para fecha: {}", fecha);

            // Validaciones rápidas
            if (authentication == null || !authentication.isAuthenticated()) {
                return ResponseEntity.status(401).body(
                        new GenericResponse<>(0, "Usuario no autenticado", null)
                );
            }

            String username = authentication.getName();
            User admin = userRepository.findByIdWithSedeAndCoordinador(
                    userRepository.findByUsername(username)
                            .orElseThrow(() -> new RuntimeException("Usuario no encontrado"))
                            .getId()
            ).orElse(null);

            if (admin == null || !Role.ADMIN.equals(admin.getRole()) || admin.getSede() == null) {
                return ResponseEntity.status(403).body(
                        new GenericResponse<>(0, "Sin permisos o sede no asignada", null)
                );
            }

            LocalDate fechaConsulta = fecha != null ? fecha : LocalDate.now();
            log.info("📊 Generando dashboard OPTIMIZADO para sede: {}", admin.getSede().getNombre());

            // 🔥 LLAMADA OPTIMIZADA
            GenericResponse<Map<String, Object>> response = asistenciaService.obtenerDashboardSede(
                    admin.getSede().getId(), fechaConsulta
            );

            log.info("✅ Dashboard OPTIMIZADO generado exitosamente");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("❌ Error OPTIMIZADO en dashboard: {}", e.getMessage(), e);
            return ResponseEntity.ok(new GenericResponse<>(0, "Error: " + e.getMessage(), null));
        }
    }


    /**
     * 🔥 NUEVO: Endpoint para cargar asistencia de usuario específico bajo demanda
     */
    @GetMapping("/admin/usuario/{usuarioId}/asistencia")
    @Transactional(readOnly = true, timeout = 5)
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerAsistenciaUsuario(
            @PathVariable Long usuarioId,
            Authentication authentication) {

        try {
            // Validaciones rápidas
            if (authentication == null || !authentication.isAuthenticated()) {
                return ResponseEntity.status(401).body(
                        new GenericResponse<>(0, "Usuario no autenticado", null)
                );
            }

            String username = authentication.getName();
            User admin = userRepository.findByUsername(username).orElse(null);

            if (admin == null || !Role.ADMIN.equals(admin.getRole())) {
                return ResponseEntity.status(403).body(
                        new GenericResponse<>(0, "Sin permisos de administrador", null)
                );
            }

            GenericResponse<Map<String, Object>> response = asistenciaService.obtenerAsistenciaUsuario(usuarioId);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("❌ Error al obtener asistencia de usuario {}: {}", usuarioId, e.getMessage());
            return ResponseEntity.ok(new GenericResponse<>(0, "Error: " + e.getMessage(), null));
        }
    }

    /**
     * 🔥 MÉTODO AUXILIAR: Validar admin con cache
     */
    private User validarAdminConCache(String username) {
        // Aquí podrías implementar un cache simple para evitar consultas repetidas
        return userRepository.findByIdWithSedeAndCoordinador(
                userRepository.findByUsername(username)
                        .orElseThrow(() -> new RuntimeException("Usuario no encontrado"))
                        .getId()
        ).orElse(null);
    }

    /**
     * 🔥 NUEVO: Obtiene reporte de productividad
     */
    @GetMapping("/admin/reporte-productividad")
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerReporteProductividad(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin,
            Authentication authentication) {

        try {
            if (authentication == null || !authentication.isAuthenticated()) {
                return ResponseEntity.status(401).body(
                        new GenericResponse<>(0, "Usuario no autenticado", null)
                );
            }

            String username = authentication.getName();
            User admin = userRepository.findByUsername(username).orElse(null);

            if (admin == null || !Role.ADMIN.equals(admin.getRole())) {
                return ResponseEntity.status(403).body(
                        new GenericResponse<>(0, "No tiene permisos de administrador", null)
                );
            }

            GenericResponse<Map<String, Object>> estadisticas = asistenciaService.obtenerEstadisticasPorFecha(fechaInicio, fechaFin);

            Map<String, Object> reporte = new HashMap<>();
            reporte.put("estadisticas", estadisticas.getData());
            reporte.put("fechaInicio", fechaInicio);
            reporte.put("fechaFin", fechaFin);
            reporte.put("sede", admin.getSede() != null ? admin.getSede().getNombre() : "N/A");

            return ResponseEntity.ok(new GenericResponse<>(1, "Reporte generado", reporte));

        } catch (Exception e) {
            log.error("Error al obtener reporte de productividad: {}", e.getMessage(), e);
            GenericResponse<Map<String, Object>> errorResponse = new GenericResponse<>(0, "Error: " + e.getMessage(), null);
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * 🔥 NUEVO: Obtiene historial de usuario
     */
    @GetMapping("/admin/historial-usuario")
    public ResponseEntity<GenericResponse<List<AsistenciaDTO>>> obtenerHistorialUsuario(
            @RequestParam Long usuarioId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin,
            Authentication authentication) {

        try {
            if (authentication == null || !authentication.isAuthenticated()) {
                return ResponseEntity.status(401).body(
                        new GenericResponse<>(0, "Usuario no autenticado", null)
                );
            }

            String username = authentication.getName();
            User admin = userRepository.findByUsername(username).orElse(null);

            if (admin == null || !Role.ADMIN.equals(admin.getRole())) {
                return ResponseEntity.status(403).body(
                        new GenericResponse<>(0, "No tiene permisos de administrador", null)
                );
            }

            GenericResponse<List<AsistenciaDTO>> response = asistenciaService.obtenerAsistenciasPorUsuarioYFechas(
                    usuarioId, fechaInicio, fechaFin);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error al obtener historial de usuario: {}", e.getMessage(), e);
            GenericResponse<List<AsistenciaDTO>> errorResponse = new GenericResponse<>(0, "Error: " + e.getMessage(), null);
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * 🔥 NUEVO: Actualiza configuración de límites
     */
    @PutMapping("/admin/configurar-limites")
    public ResponseEntity<GenericResponse<Map<String, Object>>> actualizarConfiguracionLimites(
            @RequestBody Map<String, Object> config,
            Authentication authentication) {

        try {
            if (authentication == null || !authentication.isAuthenticated()) {
                return ResponseEntity.status(401).body(
                        new GenericResponse<>(0, "Usuario no autenticado", null)
                );
            }

            String username = authentication.getName();
            User admin = userRepository.findByUsername(username).orElse(null);

            if (admin == null || !Role.ADMIN.equals(admin.getRole())) {
                return ResponseEntity.status(403).body(
                        new GenericResponse<>(0, "No tiene permisos de administrador", null)
                );
            }

            // Aquí se implementaría la lógica para actualizar configuración
            // Por ahora solo retornamos confirmación
            Map<String, Object> resultado = new HashMap<>();
            resultado.put("configuracion", config);
            resultado.put("actualizado", true);
            resultado.put("fecha", LocalDate.now());

            log.info("Admin {} actualizó configuración de límites", username);
            return ResponseEntity.ok(new GenericResponse<>(1, "Configuración actualizada", resultado));

        } catch (Exception e) {
            log.error("Error al actualizar configuración: {}", e.getMessage(), e);
            GenericResponse<Map<String, Object>> errorResponse = new GenericResponse<>(0, "Error: " + e.getMessage(), null);
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * 🔥 NUEVO: Obtiene configuración de sede
     */
    @GetMapping("/admin/configuracion-sede")
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerConfiguracionSede(
            Authentication authentication) {

        try {
            if (authentication == null || !authentication.isAuthenticated()) {
                return ResponseEntity.status(401).body(
                        new GenericResponse<>(0, "Usuario no autenticado", null)
                );
            }

            String username = authentication.getName();
            User admin = userRepository.findByUsername(username).orElse(null);

            if (admin == null || !Role.ADMIN.equals(admin.getRole())) {
                return ResponseEntity.status(403).body(
                        new GenericResponse<>(0, "No tiene permisos de administrador", null)
                );
            }

            if (admin.getSede() == null) {
                return ResponseEntity.status(400).body(
                        new GenericResponse<>(0, "Admin sin sede asignada", null)
                );
            }

            Map<String, Object> configuracion = new HashMap<>();
            configuracion.put("sede", admin.getSede());
            configuracion.put("limiteBreakMinutos", 15);
            configuracion.put("limiteBanoMinutos", 10);
            configuracion.put("maxBreaksPorDia", 2);
            configuracion.put("maxBanosPorDia", 2);
            configuracion.put("horaInicioJornada", "08:00");
            configuracion.put("horaFinJornada", "17:00");

            return ResponseEntity.ok(new GenericResponse<>(1, "Configuración obtenida", configuracion));

        } catch (Exception e) {
            log.error("Error al obtener configuración: {}", e.getMessage(), e);
            GenericResponse<Map<String, Object>> errorResponse = new GenericResponse<>(0, "Error: " + e.getMessage(), null);
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * 🔥 NUEVO: Envía notificación a usuario
     */
    @PostMapping("/admin/notificar-usuario")
    public ResponseEntity<GenericResponse<Map<String, Object>>> enviarNotificacionUsuario(
            @RequestBody Map<String, Object> body,
            Authentication authentication) {

        try {
            if (authentication == null || !authentication.isAuthenticated()) {
                return ResponseEntity.status(401).body(
                        new GenericResponse<>(0, "Usuario no autenticado", null)
                );
            }

            String username = authentication.getName();
            User admin = userRepository.findByUsername(username).orElse(null);

            if (admin == null || !Role.ADMIN.equals(admin.getRole())) {
                return ResponseEntity.status(403).body(
                        new GenericResponse<>(0, "No tiene permisos de administrador", null)
                );
            }

            Long usuarioId = Long.valueOf(body.get("usuarioId").toString());
            String mensaje = body.get("mensaje").toString();
            String tipo = body.get("tipo").toString();

            // Aquí se implementaría la lógica de notificación
            Map<String, Object> resultado = new HashMap<>();
            resultado.put("usuarioId", usuarioId);
            resultado.put("mensaje", mensaje);
            resultado.put("tipo", tipo);
            resultado.put("enviado", true);
            resultado.put("fecha", LocalDate.now());

            log.info("Admin {} envió notificación a usuario {}: {}", username, usuarioId, tipo);
            return ResponseEntity.ok(new GenericResponse<>(1, "Notificación enviada", resultado));

        } catch (Exception e) {
            log.error("Error al enviar notificación: {}", e.getMessage(), e);
            GenericResponse<Map<String, Object>> errorResponse = new GenericResponse<>(0, "Error: " + e.getMessage(), null);
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * 🔥 NUEVO: Obtiene logs de actividad
     */
    @GetMapping("/admin/logs-actividad")
    public ResponseEntity<GenericResponse<List<Map<String, Object>>>> obtenerLogsActividad(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fechaFin,
            @RequestParam(required = false, defaultValue = "100") Integer limite,
            Authentication authentication) {

        try {
            if (authentication == null || !authentication.isAuthenticated()) {
                return ResponseEntity.status(401).body(
                        new GenericResponse<>(0, "Usuario no autenticado", null)
                );
            }

            String username = authentication.getName();
            User admin = userRepository.findByUsername(username).orElse(null);

            if (admin == null || !Role.ADMIN.equals(admin.getRole())) {
                return ResponseEntity.status(403).body(
                        new GenericResponse<>(0, "No tiene permisos de administrador", null)
                );
            }

            // Por ahora retornamos logs simulados
            List<Map<String, Object>> logs = new ArrayList<>();

            Map<String, Object> log1 = new HashMap<>();
            log1.put("fecha", LocalDate.now());
            log1.put("usuario", "sistema");
            log1.put("accion", "LOGIN");
            log1.put("detalle", "Usuario autenticado correctamente");
            logs.add(log1);

            return ResponseEntity.ok(new GenericResponse<>(1, "Logs obtenidos", logs));

        } catch (Exception e) {
            log.error("Error al obtener logs: {}", e.getMessage(), e);
            GenericResponse<List<Map<String, Object>>> errorResponse = new GenericResponse<>(0, "Error: " + e.getMessage(), null);
            return ResponseEntity.ok(errorResponse);
        }
    }





    /**
     * 🔥 NUEVO: Exporta asistencias a CSV
     */
    @PostMapping("/admin/exportar-csv")
    public ResponseEntity<byte[]> exportarAsistenciasCSV(
            @Valid @RequestBody AsistenciaFilterDTO filtros,
            Authentication authentication) {

        try {
            log.info("🔥 Recibida solicitud admin/exportar-csv");

            if (authentication == null || !authentication.isAuthenticated()) {
                log.error("❌ Usuario no autenticado para exportación CSV");
                return ResponseEntity.status(401).build();
            }

            String username = authentication.getName();
            log.info("✅ Usuario autenticado para exportación CSV: {}", username);

            User admin = userRepository.findByIdWithSedeAndCoordinador(
                    userRepository.findByUsername(username)
                            .orElseThrow(() -> new RuntimeException("Usuario no encontrado"))
                            .getId()
            ).orElse(null);

            if (admin == null || !Role.ADMIN.equals(admin.getRole())) {
                log.error("❌ Usuario {} sin permisos de admin para exportación CSV", username);
                return ResponseEntity.status(403).build();
            }

            // Validar que tenga sede asignada
            if (admin.getSede() == null) {
                log.error("❌ Admin {} sin sede asignada", username);
                return ResponseEntity.status(400).build();
            }

            // Forzar filtro por sede del admin
            filtros.setSedeId(admin.getSede().getId());
            log.info("✅ Aplicando filtro de sede: {} (ID: {})", admin.getSede().getNombre(), admin.getSede().getId());

            // Validar filtros antes de exportar
            if (filtros.getFechaInicio() == null || filtros.getFechaFin() == null) {
                log.warn("⚠️ Fechas de filtro faltantes, usando fecha actual");
                LocalDate hoy = LocalDate.now();
                filtros.setFechaInicio(hoy.minusDays(30));
                filtros.setFechaFin(hoy);
            }

            // Configurar parámetros para exportación
            filtros.setPage(0);
            filtros.setSize(10000);
            filtros.setSortBy("fechaHoraEntrada");
            filtros.setSortDirection("DESC");

            log.info("📊 Generando CSV para sede: {}, fechas: {} a {}",
                    admin.getSede().getNombre(),
                    filtros.getFechaInicio(),
                    filtros.getFechaFin());

            byte[] csvBytes = asistenciaService.exportarAsistenciasCSV(filtros);

            if (csvBytes == null || csvBytes.length == 0) {
                log.error("❌ El servicio retornó un archivo CSV vacío");
                return ResponseEntity.status(500).build();
            }

            // Generar nombre de archivo
            String sedeNombre = admin.getSede().getNombre().replaceAll("[^a-zA-Z0-9]", "_");
            String fechaFormateada = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String filename = String.format("Asistencias_%s_%s.csv", sedeNombre, fechaFormateada);

            log.info("✅ CSV generado exitosamente: {} bytes, archivo: {}",
                    csvBytes.length, filename);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .header(HttpHeaders.CONTENT_TYPE, "text/csv")
                    .body(csvBytes);

        } catch (Exception e) {
            log.error("❌ Error crítico al exportar CSV: {}", e.getMessage(), e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 🔥 NUEVO: Genera reporte PDF de asistencias
     */
    @PostMapping("/admin/reporte-pdf")
    public ResponseEntity<byte[]> generarReportePDF(
            @Valid @RequestBody AsistenciaFilterDTO filtros,
            Authentication authentication) {

        try {
            log.info("🔥 Recibida solicitud admin/reporte-pdf");

            if (authentication == null || !authentication.isAuthenticated()) {
                log.error("❌ Usuario no autenticado para reporte PDF");
                return ResponseEntity.status(401).build();
            }

            String username = authentication.getName();
            User admin = userRepository.findByUsername(username).orElse(null);

            if (admin == null || !Role.ADMIN.equals(admin.getRole())) {
                log.error("❌ Usuario {} sin permisos de admin para reporte PDF", username);
                return ResponseEntity.status(403).build();
            }

            if (admin.getSede() == null) {
                log.error("❌ Admin {} sin sede asignada", username);
                return ResponseEntity.status(400).build();
            }

            // Forzar filtro por sede del admin
            filtros.setSedeId(admin.getSede().getId());

            // Validar filtros antes de generar PDF
            if (filtros.getFechaInicio() == null || filtros.getFechaFin() == null) {
                LocalDate hoy = LocalDate.now();
                filtros.setFechaInicio(hoy.minusDays(30));
                filtros.setFechaFin(hoy);
            }

            log.info("📊 Generando PDF para sede: {}, fechas: {} a {}",
                    admin.getSede().getNombre(),
                    filtros.getFechaInicio(),
                    filtros.getFechaFin());

            byte[] pdfBytes = asistenciaService.generarReportePDF(filtros);

            if (pdfBytes == null || pdfBytes.length == 0) {
                log.error("❌ El servicio retornó un PDF vacío");
                return ResponseEntity.status(500).build();
            }

            // Generar nombre de archivo
            String sedeNombre = admin.getSede().getNombre().replaceAll("[^a-zA-Z0-9]", "_");
            String fechaFormateada = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String filename = String.format("Reporte_Asistencias_%s_%s.pdf", sedeNombre, fechaFormateada);

            log.info("✅ PDF generado exitosamente: {} bytes, archivo: {}",
                    pdfBytes.length, filename);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .header(HttpHeaders.CONTENT_TYPE, "application/pdf")
                    .body(pdfBytes);

        } catch (Exception e) {
            log.error("❌ Error crítico al generar PDF: {}", e.getMessage(), e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 🔥 NUEVO: Obtiene métricas en tiempo real
     */
    @GetMapping("/admin/metricas-tiempo-real")
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerMetricasTimeReal(
            Authentication authentication) {

        try {
            if (authentication == null || !authentication.isAuthenticated()) {
                return ResponseEntity.status(401).body(
                        new GenericResponse<>(0, "Usuario no autenticado", null)
                );
            }

            String username = authentication.getName();
            User admin = userRepository.findByUsername(username).orElse(null);

            if (admin == null || !Role.ADMIN.equals(admin.getRole())) {
                return ResponseEntity.status(403).body(
                        new GenericResponse<>(0, "No tiene permisos de administrador", null)
                );
            }

            if (admin.getSede() == null) {
                return ResponseEntity.status(400).body(
                        new GenericResponse<>(0, "Admin sin sede asignada", null)
                );
            }

            // Obtener métricas en tiempo real
            LocalDate hoy = LocalDate.now();
            GenericResponse<Map<String, Object>> dashboardResponse = asistenciaService.obtenerDashboardSede(
                    admin.getSede().getId(), hoy);

            Map<String, Object> metricas = new HashMap<>();
            if (dashboardResponse.getData() != null) {
                metricas.putAll(dashboardResponse.getData());
            }

            // Agregar timestamp para tiempo real
            metricas.put("timestamp", LocalDateTime.now());
            metricas.put("sede", admin.getSede().getNombre());

            return ResponseEntity.ok(new GenericResponse<>(1, "Métricas en tiempo real", metricas));

        } catch (Exception e) {
            log.error("Error al obtener métricas en tiempo real: {}", e.getMessage(), e);
            GenericResponse<Map<String, Object>> errorResponse = new GenericResponse<>(0, "Error: " + e.getMessage(), null);
            return ResponseEntity.ok(errorResponse);
        }
    }
}
