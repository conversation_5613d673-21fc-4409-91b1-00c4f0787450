package com.midas.crm.entity.DTO.encuesta;

import com.midas.crm.entity.Encuesta;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO para actualizar una encuesta existente
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EncuestaUpdateDTO {

    private String titulo;

    private String descripcion;

    private LocalDateTime fechaInicio;

    private LocalDateTime fechaFin;

    private Integer tiempoLimite;

    private Boolean esAnonima;

    private Boolean mostrarResultados;

    private Encuesta.TipoAsignacion tipoAsignacion;

    // ID de la sede (si tipoAsignacion = SEDE)
    private Long sedeId;

    // ID del coordinador (si tipoAsignacion = COORDINACION)
    private Long coordinadorId;

    // ID del usuario específico (si tipoAsignacion = PERSONAL y es un solo usuario)
    private Long usuarioId;

    // IDs de usuarios específicos (si tipoAsignacion = PERSONAL y son múltiples usuarios)
    private List<Long> usuarioIds;

    private String estado;
}
