package com.midas.crm.entity.DTO.curso;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO optimizado para la lista de cursos en el frontend
 * Solo incluye los campos necesarios para la vista de lista
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CursoListDTO {
    private Long id;
    private String nombre;
    private String descripcion;
    private LocalDateTime fechaInicio;
    private LocalDateTime fechaFin;
    private String estado;
    private String videoUrl;

    // Información mínima del usuario creador
    private String usuarioNombre;
    private String usuarioApellido;

    // Información de asignación si existe
    private CursoUsuarioMinimalDTO asignacion;

    // Estadísticas del curso
    private Integer totalModulos;
    private Integer totalSecciones;
    private Integer totalLecciones;
}
