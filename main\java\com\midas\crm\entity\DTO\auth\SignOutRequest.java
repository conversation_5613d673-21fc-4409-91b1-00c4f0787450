package com.midas.crm.entity.DTO.auth;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SignOutRequest {

    private Long userId;

    /**
     * 🔥 NUEVO: Motivo del cierre de sesión
     * Valores posibles:
     * - MANUAL: Usuario hace logout consciente
     * - INACTIVIDAD: Timeout por inactividad
     * - SUSPENSION: Sistema se suspende
     * - TECNICO: Error técnico/conexión
     * - TOKEN_EXPIRY: Token expira
     * - FORZADO: Cierre forzado del navegador
     */
    private String motivo;

    /**
     * 🔥 NUEVO: Información adicional del contexto
     * Puede incluir detalles como tiempo de inactividad,
     * tipo de error, etc.
     */
    private String detalles;

    // Constructor para compatibilidad hacia atrás
    public SignOutRequest(Long userId) {
        this.userId = userId;
        this.motivo = "MANUAL"; // Por defecto es manual
    }
}