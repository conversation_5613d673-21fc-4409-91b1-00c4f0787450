package com.midas.crm.entity.DTO.user;

import com.midas.crm.entity.Role;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO simplificado para representar un coordinador en el listado de usuarios
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserCoordinadorDTO {
    private Long id;
    private String username;
    private String nombre;
    private String apellido;
    private String dni;
    private String telefono;
    private String email;
    private String sede;
    private Long sede_id;
}
