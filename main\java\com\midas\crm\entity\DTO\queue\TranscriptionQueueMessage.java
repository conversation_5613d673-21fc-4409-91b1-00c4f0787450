package com.midas.crm.entity.DTO.queue;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO para mensajes de cola de transcripción
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TranscriptionQueueMessage {

    private Long leadId;
    private String numeroMovil;
    private String numeroAgente;
    private String numeroAgenteNormalizado;
    private String nombreCliente;
    private String dniCliente;
    private LocalDateTime fechaCreacion;
    private String nombreAsesor;
    private String archivoMp3Url;
    private String nombreArchivoMp3;
    private String estadoProcesamiento; // PENDING, PROCESSING, COMPLETED, FAILED
    private String mensajeError;
    private LocalDateTime fechaEnvio;
    private Integer intentos;
    private Integer maxIntentos;

    // Tipo de procesamiento: FULL (transcripción + comparación), TRANSCRIPTION_ONLY, COMPARISON_ONLY
    private String processType;

    // Metadatos adicionales para el procesamiento
    private String whisperModel;
    private String device;
    private String targetLanguage;
    private String callType;
    private String callId;
    private String agentId;
    private String callDatetime;

    // Información de la cola
    private String queueName;
    private String routingKey;

    public TranscriptionQueueMessage(Long leadId, String numeroMovil, String numeroAgente,
                                     String nombreCliente, String dniCliente, LocalDateTime fechaCreacion,
                                     String nombreAsesor, String archivoMp3Url, String nombreArchivoMp3) {
        this.leadId = leadId;
        this.numeroMovil = numeroMovil;
        this.numeroAgente = numeroAgente;
        this.numeroAgenteNormalizado = normalizarNumeroAgente(numeroAgente);
        this.nombreCliente = nombreCliente;
        this.dniCliente = dniCliente;
        this.fechaCreacion = fechaCreacion;
        this.nombreAsesor = nombreAsesor;
        this.archivoMp3Url = archivoMp3Url;
        this.nombreArchivoMp3 = nombreArchivoMp3;
        this.estadoProcesamiento = "PENDING";
        this.fechaEnvio = LocalDateTime.now();
        this.intentos = 0;
        this.maxIntentos = 3;

        // Valores por defecto para transcripción
        this.whisperModel = "base";
        this.device = "cpu";
        this.targetLanguage = "es";
        this.callType = "inbound";
        this.callId = generateCallId();
        this.agentId = this.numeroAgenteNormalizado;
        this.callDatetime = this.fechaCreacion.toString();
    }

    /**
     * Normaliza el número de agente para manejar diferentes formatos
     * Ejemplos: 017 -> 17, agen017 -> 17, 11 -> 11
     */
    private String normalizarNumeroAgente(String numeroAgente) {
        if (numeroAgente == null || numeroAgente.trim().isEmpty()) {
            return null;
        }

        String agente = numeroAgente.trim().toLowerCase();

        // Remover prefijo "agen" si existe
        if (agente.startsWith("agen")) {
            agente = agente.substring(4);
        }

        // Remover ceros a la izquierda
        agente = agente.replaceFirst("^0+", "");

        // Si quedó vacío después de remover ceros, devolver "0"
        if (agente.isEmpty()) {
            agente = "0";
        }

        // Validar que solo contenga números
        if (!agente.matches("\\d+")) {
            return numeroAgente; // Devolver original si no es numérico
        }

        return agente;
    }

    /**
     * Genera un ID único para la llamada
     */
    private String generateCallId() {
        String timestamp = LocalDateTime.now().toString().replaceAll("[^0-9]", "");
        String random = String.valueOf((int)(Math.random() * 10000));
        String phone = this.numeroMovil != null && this.numeroMovil.length() >= 4
                ? this.numeroMovil.substring(this.numeroMovil.length() - 4)
                : "0000";

        return String.format("CALL_%s_%s_%s", timestamp, phone, random);
    }

    /**
     * Incrementa el contador de intentos
     */
    public void incrementarIntentos() {
        this.intentos = (this.intentos == null ? 0 : this.intentos) + 1;
    }

    /**
     * Verifica si se han agotado los intentos máximos
     */
    public boolean intentosAgotados() {
        return this.intentos != null && this.maxIntentos != null && this.intentos >= this.maxIntentos;
    }

    /**
     * Marca el mensaje como procesando
     */
    public void marcarComoProcesando() {
        this.estadoProcesamiento = "PROCESSING";
    }

    /**
     * Marca el mensaje como completado
     */
    public void marcarComoCompletado() {
        this.estadoProcesamiento = "COMPLETED";
    }

    /**
     * Marca el mensaje como fallido
     */
    public void marcarComoFallido(String mensajeError) {
        this.estadoProcesamiento = "FAILED";
        this.mensajeError = mensajeError;
    }
}
