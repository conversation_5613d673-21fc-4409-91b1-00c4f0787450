package com.midas.crm.entity.DTO.encuesta;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO para crear un nuevo detalle de respuesta de usuario a una pregunta de encuesta
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DetalleRespuestaEncuestaUsuarioCreateDTO {
    private Long respuestaEncuestaUsuarioId;
    private Long preguntaId;
    private Long opcionId; // Puede ser null para preguntas de texto libre, fecha o número
    private List<Long> opcionIds;
    private String respuestaTexto; // Para preguntas de texto libre
    private Double respuestaNumero; // Para preguntas numéricas
    private LocalDateTime respuestaFecha; // Para preguntas de fecha

    public List<Long> getOpcionIds() {
        return opcionIds;
    }

    public void setOpcionIds(List<Long> opcionIds) {
        this.opcionIds = opcionIds;
    }
}