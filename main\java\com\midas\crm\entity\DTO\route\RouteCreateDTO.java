package com.midas.crm.entity.DTO.route;

import com.midas.crm.entity.Role;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RouteCreateDTO {

    @NotBlank(message = "El path es obligatorio")
    private String path;

    @NotBlank(message = "El nombre es obligatorio")
    private String name;

    private String description;
    private String componentName;
    private String modulePath;

    @NotNull(message = "El estado activo es obligatorio")
    private Boolean isActive = true;

    @NotNull(message = "El campo requiresAuth es obligatorio")
    private Boolean requiresAuth = true;

    private List<Role> allowedRoles;
}
