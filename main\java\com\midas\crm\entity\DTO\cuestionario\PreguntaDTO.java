package com.midas.crm.entity.DTO.cuestionario;

import com.midas.crm.entity.Pregunta.TipoPregunta;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PreguntaDTO {
    private Long id;
    private String enunciado;
    private String explicacion;
    private Integer puntaje;
    private Integer orden;
    private TipoPregunta tipo;
    private Long cuestionarioId;
    private String estado;
    private LocalDateTime fechaCreacion;
    private LocalDateTime fechaActualizacion;
    private List<RespuestaDTO> respuestas;
}
