<div class="min-h-screen bg-transparent">
  <!-- 🔥 HEADER ADMIN -->
  <div class="relative pb-4">
    <div class="relative z-10">
      <div class="flex flex-col md:flex-row items-center justify-between p-3 md:p-4">
        <!-- <PERSON><PERSON><PERSON><PERSON> -->
        <div class="text-gray-900 font-bold text-lg md:text-xl flex items-center">
          <i class="fas fa-user-shield mr-2 text-blue-600"></i>
          Panel de Administración - {{ sedeInfo?.nombre }}
        </div>

        <!-- Controles de fecha y exportación -->
        <div class="flex items-center space-x-2">
          <input
            type="date"
            [(ngModel)]="fechaSeleccionada"
            (change)="onFechaChange()"
            class="px-3 py-1.5 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500"
          />
          <button
            (click)="cargarDashboard()"
            [disabled]="loading"
            class="px-3 py-1.5 bg-blue-500 text-white rounded-lg text-sm hover:bg-blue-600 transition-colors disabled:opacity-50"
            title="Actualizar dashboard"
          >
            <i class="fas fa-sync-alt mr-1"></i>
            Actualizar
          </button>
          
          <!-- 🔥 BOTÓN DE EXPORTACIÓN PRINCIPAL -->
          <button
            (click)="exportarExcel()"
            [disabled]="loading || totalRegistros === 0"
            class="px-3 py-1.5 bg-green-500 text-white rounded-lg text-sm hover:bg-green-600 transition-colors disabled:opacity-50 flex items-center relative"
            title="Exportar asistencias a Excel"
          >
            <i class="fas fa-file-excel mr-1"></i>
            <span class="hidden sm:inline">Excel</span>
            <span class="sm:hidden">📊</span>
            
            <!-- Badge de registros -->
            <span 
              *ngIf="totalRegistros > 0"
              class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full px-1 py-0.5 min-w-[16px] text-center leading-none"
            >
              {{ totalRegistros > 99 ? '99+' : totalRegistros }}
            </span>
          </button>
        </div>
      </div>

      <!-- 🔥 DASHBOARD ESTADÍSTICAS -->
      <div class="mx-3 md:mx-4 mb-4" *ngIf="dashboardData">
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
          <!-- Total Usuarios -->
          <div class="bg-white/80 backdrop-blur-sm rounded-lg shadow-sm border border-gray-200 p-3">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-xs text-gray-600">Total Usuarios</p>
                <p class="text-lg font-bold text-gray-900">{{ dashboardData.totalUsuarios }}</p>
              </div>
              <i class="fas fa-users text-blue-500 text-lg"></i>
            </div>
          </div>

          <!-- Usuarios con Entrada -->
          <div class="bg-white/80 backdrop-blur-sm rounded-lg shadow-sm border border-gray-200 p-3">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-xs text-gray-600">Con Entrada</p>
                <p class="text-lg font-bold text-green-600">{{ dashboardData.usuariosConEntrada }}</p>
              </div>
              <i class="fas fa-sign-in-alt text-green-500 text-lg"></i>
            </div>
          </div>

          <!-- Usuarios Trabajando -->
          <div class="bg-white/80 backdrop-blur-sm rounded-lg shadow-sm border border-gray-200 p-3">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-xs text-gray-600">Trabajando</p>
                <p class="text-lg font-bold text-blue-600">{{ dashboardData.usuariosTrabajando }}</p>
              </div>
              <i class="fas fa-briefcase text-blue-500 text-lg"></i>
            </div>
          </div>

          <!-- Usuarios con Salida -->
          <div class="bg-white/80 backdrop-blur-sm rounded-lg shadow-sm border border-gray-200 p-3">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-xs text-gray-600">Con Salida</p>
                <p class="text-lg font-bold text-red-600">{{ dashboardData.usuariosConSalida }}</p>
              </div>
              <i class="fas fa-sign-out-alt text-red-500 text-lg"></i>
            </div>
          </div>

          <!-- Breaks Activos -->
          <div class="bg-white/80 backdrop-blur-sm rounded-lg shadow-sm border border-gray-200 p-3">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-xs text-gray-600">Breaks Activos</p>
                <p class="text-lg font-bold text-yellow-600">{{ dashboardData.breaksActivos }}</p>
              </div>
              <i class="fas fa-coffee text-yellow-500 text-lg"></i>
            </div>
          </div>

          <!-- Baños Activos -->
          <div class="bg-white/80 backdrop-blur-sm rounded-lg shadow-sm border border-gray-200 p-3">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-xs text-gray-600">Baños Activos</p>
                <p class="text-lg font-bold text-cyan-600">{{ dashboardData.banosActivos }}</p>
              </div>
              <i class="fas fa-restroom text-cyan-500 text-lg"></i>
            </div>
          </div>
        </div>

        <!-- Promedio de Horas -->
        <div class="mt-3 bg-white/80 backdrop-blur-sm rounded-lg shadow-sm border border-gray-200 p-3">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">Promedio de Horas Trabajadas</p>
              <p class="text-2xl font-bold text-gray-900">
                {{ dashboardData.promedioHorasTrabajadas | number : "1.1-1" }}h
              </p>
            </div>
            <div class="text-right">
              <p class="text-xs text-gray-500">Fecha: {{ fechaSeleccionada | date : "dd/MM/yyyy" }}</p>
              <p class="text-xs text-gray-500">{{ dashboardData.sede?.ciudad }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 🔥 CONTENIDO PRINCIPAL -->
  <div class="max-w-7xl mx-auto px-4 py-4">
    <!-- Filtros -->
    <div class="mb-4">
      <div class="bg-white/80 backdrop-blur-sm rounded-lg shadow-sm border border-gray-200 p-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-3">
          <!-- Filtro Usuario con Paginación -->
          <div class="relative">
            <label class="block text-xs font-medium text-gray-700 mb-1">
              Usuario
              <span class="text-gray-500">({{ usuariosPaginacion.totalElements }} total)</span>
            </label>

            <!-- Dropdown con búsqueda -->
            <div class="relative">
              <select
                [(ngModel)]="filtros.usuarioId"
                (change)="aplicarFiltros()"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500"
              >
                <option [value]="null">Todos los usuarios</option>
                <option *ngFor="let usuario of usuariosSede" [value]="usuario.id">
                  {{ usuario.nombre }} ({{ usuario.username }})
                </option>
              </select>

              <!-- Indicador de carga -->
              <div *ngIf="loadingUsuariosPaginados"
                   class="absolute right-8 top-1/2 transform -translate-y-1/2">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
              </div>
            </div>

            <!-- Panel de búsqueda y paginación (expandible) -->
            <div class="mt-2 p-2 bg-gray-50 rounded border text-xs">
              <!-- Búsqueda -->
              <div class="flex gap-2 mb-2">
                <input
                  type="text"
                  [(ngModel)]="usuariosSearch"
                  (keyup.enter)="onUsuariosSearch()"
                  placeholder="Buscar por nombre, apellido o username..."
                  class="flex-1 px-2 py-1 border border-gray-300 rounded text-xs focus:ring-1 focus:ring-blue-500"
                >
                <button
                  (keyup.enter)="onUsuariosSearch()"
                  [disabled]="loadingUsuariosPaginados"
                  class="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
                >
                  🔍
                </button>
              </div>

              <!-- Paginación -->
              <div class="flex items-center justify-between">
                <div class="text-gray-600">
                  Página {{ usuariosPaginacion.currentPage + 1 }} de {{ usuariosPaginacion.totalPages }}
                </div>
                <div class="flex gap-1">
                  <button
                    (click)="irPaginaAnterior()"
                    [disabled]="!usuariosPaginacion.hasPrevious || loadingUsuariosPaginados"
                    class="px-2 py-1 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    ←
                  </button>
                  <button
                    (click)="irPaginaSiguiente()"
                    [disabled]="!usuariosPaginacion.hasNext || loadingUsuariosPaginados"
                    class="px-2 py-1 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    →
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Filtro Tipo Actividad -->
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">Tipo Actividad</label>
            <select
              [(ngModel)]="filtros.tipoActividad"
              (change)="aplicarFiltros()"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500"
            >
              <option [value]="null">Todas las actividades</option>
              <option value="ENTRADA">Entrada</option>
              <option value="SALIDA">Salida</option>
              <option value="BREAK">Break</option>
              <option value="BANO">Baño</option>
              <option value="SESION_CRM">Sesión CRM</option>
            </select>
          </div>

          <!-- Filtro Fecha Inicio -->
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">Fecha Inicio</label>
            <input
              type="date"
              [(ngModel)]="filtros.fechaInicio"
              (change)="aplicarFiltros()"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <!-- Filtro Fecha Fin -->
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">Fecha Fin</label>
            <input
              type="date"
              [(ngModel)]="filtros.fechaFin"
              (change)="aplicarFiltros()"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <!-- Botones de acción -->
        <div class="flex justify-between items-center mt-3">
          <button
            (click)="limpiarFiltros()"
            class="px-3 py-1.5 text-gray-600 hover:text-gray-800 text-sm transition-colors"
          >
            <i class="fas fa-eraser mr-1"></i>
            Limpiar Filtros
          </button>

          <div class="flex space-x-2">
            <button
              (click)="aplicarFiltros()"
              [disabled]="loading"
              class="px-4 py-1.5 bg-blue-500 text-white rounded-lg text-sm hover:bg-blue-600 transition-colors disabled:opacity-50"
            >
              <i class="fas fa-search mr-1"></i>
              Buscar
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Top Usuarios -->
    <div class="mb-4" *ngIf="hasTopUsuarios()">
      <div class="bg-white/80 backdrop-blur-sm rounded-lg shadow-sm border border-gray-200 p-4">
        <h3 class="text-sm font-semibold text-gray-900 mb-3 flex items-center">
          <i class="fas fa-trophy mr-2 text-yellow-500"></i>
          Top 5 Usuarios por Tiempo Trabajado
        </h3>

        <div class="space-y-2">
          <div
            *ngFor="let usuario of getTopUsuarios(); let i = index"
            class="flex items-center justify-between p-2 rounded border border-gray-100"
          >
            <div class="flex items-center space-x-3">
              <span
                class="flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold"
                [ngClass]="{
                  'bg-yellow-100 text-yellow-800': i === 0,
                  'bg-gray-100 text-gray-800': i === 1,
                  'bg-orange-100 text-orange-800': i === 2,
                  'bg-blue-100 text-blue-800': i > 2
                }"
              >
                {{ i + 1 }}
              </span>
              <span class="text-sm font-medium text-gray-900">{{ usuario.usuario }}</span>
            </div>
            <div class="text-right">
              <span class="text-sm font-bold text-gray-900">{{ usuario.tiempoHoras }}h</span>
              <span class="text-xs text-gray-500 ml-2">{{ usuario.porcentaje | number : "1.0-0" }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tabla de Asistencias -->
    <div class="bg-white/80 backdrop-blur-sm rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <!-- Header de la tabla -->
      <div class="px-4 py-3 border-b border-gray-200 flex items-center justify-between">
        <h3 class="text-sm font-semibold text-gray-900 flex items-center">
          <i class="fas fa-table mr-2 text-gray-600"></i>
          Registros de Asistencia
          <span class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
            {{ totalRegistros }} registros
          </span>
        </h3>

        <!-- Info de exportación -->
        <div class="text-xs text-gray-500">
          <span *ngIf="totalRegistros > 0" class="text-green-600">
            <i class="fas fa-download mr-1"></i>
            Listos para exportar
          </span>
          <span *ngIf="totalRegistros === 0" class="text-red-500">
            Sin datos para exportar
          </span>
        </div>
      </div>

      <!-- Loading -->
      <div *ngIf="loading" class="p-8 text-center">
        <div class="inline-flex items-center space-x-2 text-gray-600">
          <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
          <span>Cargando registros...</span>
        </div>
      </div>

      <!-- Tabla -->
      <div *ngIf="!loading" class="overflow-x-auto">
        <table class="w-full text-sm">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Usuario</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actividad</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fecha/Hora</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duración</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Estado</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Breaks/Baños</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tiempo Neto</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr *ngFor="let asistencia of asistencias" class="hover:bg-gray-50">
              <!-- Usuario -->
              <td class="px-3 py-2">
                <div>
                  <div class="text-sm font-medium text-gray-900">{{ asistencia.usuarioNombre }}</div>
                  <div class="text-xs text-gray-500">{{ asistencia.usuarioUsername }}</div>
                </div>
              </td>

              <!-- Actividad -->
              <td class="px-3 py-2">
                <span
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                  [ngClass]="{
                    'bg-green-100 text-green-800': asistencia.tipoActividad === 'ENTRADA',
                    'bg-red-100 text-red-800': asistencia.tipoActividad === 'SALIDA',
                    'bg-yellow-100 text-yellow-800': asistencia.tipoActividad === 'BREAK',
                    'bg-blue-100 text-blue-800': asistencia.tipoActividad === 'BANO',
                    'bg-purple-100 text-purple-800': asistencia.tipoActividad === 'SESION_CRM'
                  }"
                >
                  <i
                    class="mr-1"
                    [ngClass]="{
                      'fas fa-sign-in-alt': asistencia.tipoActividad === 'ENTRADA',
                      'fas fa-sign-out-alt': asistencia.tipoActividad === 'SALIDA',
                      'fas fa-coffee': asistencia.tipoActividad === 'BREAK',
                      'fas fa-restroom': asistencia.tipoActividad === 'BANO',
                      'fas fa-desktop': asistencia.tipoActividad === 'SESION_CRM'
                    }"
                  ></i>
                  {{ getTipoActividadLabel(asistencia.tipoActividad) }}
                </span>
              </td>

              <!-- Fecha/Hora -->
              <td class="px-3 py-2">
                <div class="text-sm text-gray-900">
                  {{ asistencia.fechaHoraEntrada | date : "dd/MM/yyyy HH:mm" }}
                </div>
                <div class="text-xs text-gray-500" *ngIf="asistencia.fechaHoraSalida">
                  Fin: {{ asistencia.fechaHoraSalida | date : "HH:mm" }}
                </div>
              </td>

              <!-- Duración -->
              <td class="px-3 py-2">
                <div class="text-sm text-gray-900" *ngIf="asistencia.duracionMinutos">
                  {{ formatearMinutos(asistencia.duracionMinutos) }}
                </div>
                <div class="text-xs text-gray-500" *ngIf="!asistencia.duracionMinutos && !asistencia.fechaHoraSalida">
                  En curso...
                </div>
              </td>

              <!-- Estado -->
              <td class="px-3 py-2">
                <span
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs"
                  [ngClass]="{
                    'bg-green-100 text-green-800': asistencia.estadoActual === 'TRABAJANDO',
                    'bg-yellow-100 text-yellow-800': asistencia.estadoActual === 'EN_BREAK',
                    'bg-blue-100 text-blue-800': asistencia.estadoActual === 'EN_BANO',
                    'bg-gray-100 text-gray-800': asistencia.estadoActual === 'SALIDA'
                  }"
                >
                  {{ getEstadoLabel(asistencia.estadoActual) }}
                </span>
              </td>

              <!-- Breaks/Baños -->
              <td class="px-3 py-2">
                <div class="flex space-x-3 text-xs">
                  <span class="flex items-center space-x-1">
                    <i class="fas fa-coffee text-yellow-500"></i>
                    <span>{{ asistencia.breakContador || 0 }}/2</span>
                    <span class="text-gray-500" *ngIf="asistencia.breakTiempoTotalMinutos">
                      ({{ asistencia.breakTiempoTotalMinutos }}m)
                    </span>
                  </span>
                  <span class="flex items-center space-x-1">
                    <i class="fas fa-restroom text-blue-500"></i>
                    <span>{{ asistencia.banoContador || 0 }}/2</span>
                    <span class="text-gray-500" *ngIf="asistencia.banoTiempoTotalMinutos">
                      ({{ asistencia.banoTiempoTotalMinutos }}m)
                    </span>
                  </span>
                </div>
              </td>

              <!-- Tiempo Neto -->
              <td class="px-3 py-2">
                <div class="text-sm font-medium text-gray-900" *ngIf="asistencia.tiempoNetoTrabajadoMinutos">
                  {{ formatearMinutos(asistencia.tiempoNetoTrabajadoMinutos) }}
                </div>
                <div class="text-xs text-gray-500" *ngIf="asistencia.porcentajeJornada">
                  {{ asistencia.porcentajeJornada | number : "1.0-0" }}% jornada
                </div>
              </td>
            </tr>

            <!-- Sin registros -->
            <tr *ngIf="!loading && asistencias.length === 0">
              <td colspan="7" class="px-3 py-8 text-center text-gray-500">
                <div class="flex flex-col items-center space-y-2">
                  <i class="fas fa-inbox text-3xl text-gray-300"></i>
                  <span>No se encontraron registros de asistencia</span>
                  <span class="text-xs">Ajuste los filtros para ver más resultados</span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Paginación -->
      <div class="px-4 py-3 border-t border-gray-200 flex items-center justify-between" *ngIf="totalPaginas > 1">
        <div class="text-xs text-gray-500">
          Mostrando {{ getStartRecord() }} - {{ getEndRecord() }} de {{ totalRegistros }} registros
        </div>

        <div class="flex space-x-1">
          <button
            (click)="cambiarPagina(paginaActual - 1)"
            [disabled]="paginaActual <= 1"
            class="px-2 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <i class="fas fa-chevron-left"></i>
          </button>

          <span class="px-2 py-1 text-xs text-gray-700">{{ paginaActual }} / {{ totalPaginas }}</span>

          <button
            (click)="cambiarPagina(paginaActual + 1)"
            [disabled]="paginaActual >= totalPaginas"
            class="px-2 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>