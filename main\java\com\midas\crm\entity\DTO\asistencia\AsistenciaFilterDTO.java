package com.midas.crm.entity.DTO.asistencia;

import com.midas.crm.entity.Asistencia;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AsistenciaFilterDTO {
    
    private Long usuarioId;
    private LocalDate fechaInicio;
    private LocalDate fechaFin;
    private LocalDateTime fechaHoraInicio;
    private LocalDateTime fechaHoraFin;
    private Asistencia.TipoActividad tipoActividad;
    private Asistencia.SubtipoActividad subtipoActividad;
    private String estado;
    private Long sedeId;
    
    // Paginación
    private Integer page = 0;
    private Integer size = 20;
    private String sortBy = "fechaHoraEntrada";
    private String sortDirection = "DESC";
}
