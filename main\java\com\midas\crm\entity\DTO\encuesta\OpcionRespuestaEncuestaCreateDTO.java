package com.midas.crm.entity.DTO.encuesta;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO para crear una nueva opción de respuesta de encuesta
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OpcionRespuestaEncuestaCreateDTO {
    
    @NotBlank(message = "El texto es obligatorio")
    private String texto;
    
    private Integer orden;
    
    private Integer valor;
    
    private Long preguntaId;
}
