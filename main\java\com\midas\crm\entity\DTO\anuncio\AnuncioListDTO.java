package com.midas.crm.entity.DTO.anuncio;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AnuncioListDTO {
    private Long id;
    private String titulo;
    private String descripcion;
    private String imagenUrl;
    private String categoria;
    private String fechaPublicacion;
    private String fechaInicio;
    private String fechaFin;
    private Integer orden;
    private String estado;
    private String nombreUsuario;
    private Long sedeId;
    private String nombreSede;
}
