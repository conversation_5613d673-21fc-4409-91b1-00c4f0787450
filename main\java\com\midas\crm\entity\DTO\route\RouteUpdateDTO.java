package com.midas.crm.entity.DTO.route;

import com.midas.crm.entity.Role;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RouteUpdateDTO {
    private String name;
    private String description;
    private String componentName;
    private String modulePath;
    private Boolean isActive;
    private Boolean requiresAuth;
    private List<Role> allowedRoles;
}
