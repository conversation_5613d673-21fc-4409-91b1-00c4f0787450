package com.midas.crm.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

@Entity
@Table(name = "lecciones")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Leccion {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, length = 100)
    private String titulo;

    @Column(length = 500)
    private String descripcion;

    @Column(nullable = false)
    private Integer orden;

    @Enumerated(EnumType.STRING)
    @Column(name = "tipo_leccion", nullable = false)
    private TipoLeccion tipoLeccion = TipoLeccion.VIDEO; // Tipo de lección: VIDEO o CUESTIONARIO

    @Column(name = "video_url", length = 500)
    private String videoUrl; // URL del video almacenado en Firebase

    @Column(name = "subtitles_url", length = 500)
    private String subtitlesUrl; // URL de los subtítulos del video

    @Column(length = 10)
    private String duracion; // Formato "HH:MM:SS"

    @Column(name = "thumbnail_url", length = 500)
    private String thumbnailUrl; // URL de la miniatura del video

    @Column(name = "pdf_url", columnDefinition = "TEXT")
    private String pdfUrl; // URL del PDF almacenado en Firebase o múltiples URLs separadas por comas

    @OneToOne(mappedBy = "leccion", cascade = CascadeType.ALL, orphanRemoval = true)
    private Cuestionario cuestionario; // Cuestionario asociado a la lección (si es de tipo CUESTIONARIO)

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "seccion_id", nullable = false)
    private Seccion seccion;

    @Column(nullable = false, length = 1)
    private String estado = "A"; // A: Activo, I: Inactivo

    @CreationTimestamp
    @Column(name = "fecha_creacion", updatable = false)
    private LocalDateTime fechaCreacion;

    @UpdateTimestamp
    @Column(name = "fecha_actualizacion")
    private LocalDateTime fechaActualizacion;

    /**
     * Enum que define los tipos de lecciones disponibles
     */
    public enum TipoLeccion {
        VIDEO,         // Lección con video
        CUESTIONARIO,  // Lección con cuestionario
        PDF            // Lección con documento PDF
    }
}
