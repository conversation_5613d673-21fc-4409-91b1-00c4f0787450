package com.midas.crm.entity.DTO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO para representar archivos de Google Drive
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GoogleDriveFileDTO {

    private String id;
    private String name;
    private String mimeType;
    private Long size;
    private String webContentLink;
    private String webViewLink;
    private String thumbnailLink;
    private String createdTime;
    private String modifiedTime;
    private String description;
    private boolean trashed;
    private String parents;

    // Campos adicionales para el frontend
    private String downloadUrl;
    private String publicUrl;
    private boolean isFolder;
    private String extension;
    private String sizeFormatted;

    /**
     * Constructor simplificado para casos básicos
     */
    public GoogleDriveFileDTO(String id, String name, String mimeType, Long size) {
        this.id = id;
        this.name = name;
        this.mimeType = mimeType;
        this.size = size;
        this.isFolder = "application/vnd.google-apps.folder".equals(mimeType);
        this.extension = extractExtension(name);
    }

    /**
     * Extrae la extensión del archivo
     */
    private String extractExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
    }

    /**
     * Verifica si es un archivo de audio
     */
    public boolean isAudioFile() {
        if (mimeType != null && mimeType.startsWith("audio/")) {
            return true;
        }

        String ext = getExtension();
        return ext.matches("mp3|wav|m4a|aac|ogg|webm|flac|mp4|avi|mov|wmv|3gp");
    }

    /**
     * Verifica si es un archivo de video
     */
    public boolean isVideoFile() {
        if (mimeType != null && mimeType.startsWith("video/")) {
            return true;
        }

        String ext = getExtension();
        return ext.matches("mp4|avi|mov|wmv|flv|webm|mkv|3gp|m4v");
    }

    /**
     * Verifica si es un archivo de texto
     */
    public boolean isTextFile() {
        if (mimeType != null && mimeType.startsWith("text/")) {
            return true;
        }

        String ext = getExtension();
        return ext.matches("txt|doc|docx|pdf|rtf|odt");
    }

    /**
     * Obtiene el tamaño formateado
     */
    public String getFormattedSize() {
        if (size == null || size == 0) {
            return "0 B";
        }

        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double fileSize = size.doubleValue();

        while (fileSize >= 1024 && unitIndex < units.length - 1) {
            fileSize /= 1024;
            unitIndex++;
        }

        return String.format("%.1f %s", fileSize, units[unitIndex]);
    }

    /**
     * Verifica si el archivo es válido para transcripción
     */
    public boolean isValidForTranscription() {
        return isAudioFile() || isVideoFile();
    }

    /**
     * Obtiene el icono apropiado para el tipo de archivo
     */
    public String getFileIcon() {
        if (isFolder) {
            return "folder";
        } else if (isAudioFile()) {
            return "audiotrack";
        } else if (isVideoFile()) {
            return "videocam";
        } else if (isTextFile()) {
            return "description";
        } else {
            return "insert_drive_file";
        }
    }

    /**
     * Obtiene la clase CSS para el tipo de archivo
     */
    public String getFileTypeClass() {
        if (isFolder) {
            return "file-folder";
        } else if (isAudioFile()) {
            return "file-audio";
        } else if (isVideoFile()) {
            return "file-video";
        } else if (isTextFile()) {
            return "file-text";
        } else {
            return "file-other";
        }
    }
}
