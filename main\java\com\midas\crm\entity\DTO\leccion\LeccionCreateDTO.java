package com.midas.crm.entity.DTO.leccion;

import com.midas.crm.entity.Leccion.TipoLeccion;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LeccionCreateDTO {
    @NotBlank(message = "El título de la lección es obligatorio")
    private String titulo;

    private String descripcion;

    @NotNull(message = "El orden de la lección es obligatorio")
    private Integer orden;

    private TipoLeccion tipoLeccion = TipoLeccion.VIDEO;

    private String videoUrl;

    private String subtitlesUrl;

    private String duracion;

    private String thumbnailUrl;

    private String pdfUrl;

    @NotNull(message = "El ID de la sección es obligatorio")
    private Long seccionId;
}
