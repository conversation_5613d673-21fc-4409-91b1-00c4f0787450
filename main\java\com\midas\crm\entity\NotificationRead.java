package com.midas.crm.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Entidad para registrar las notificaciones leídas por los usuarios
 * Especialmente útil para notificaciones broadcast y basadas en rol
 */
@Entity
@Table(name = "notification_read")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NotificationRead {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "notification_id")
    private Long notificationId;

    @Column(name = "read_at")
    private LocalDateTime readAt = LocalDateTime.now();

    // Constructor sin fecha (para compatibilidad)
    public NotificationRead(Long userId, Long notificationId) {
        this.userId = userId;
        this.notificationId = notificationId;
        this.readAt = LocalDateTime.now();
    }
}
