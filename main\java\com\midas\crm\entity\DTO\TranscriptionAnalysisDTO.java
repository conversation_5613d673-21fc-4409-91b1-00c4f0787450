package com.midas.crm.entity.DTO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * DTO para mostrar información de análisis de transcripción
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TranscriptionAnalysisDTO {

    private Long id;
    private Long clienteResidencialId;
    private String nombreCliente;
    private String movilContacto;
    private String numeroAgente;
    
    // Datos del análisis
    private BigDecimal porcentajePromedio;
    private BigDecimal porcentajePonderado;
    private String nivelConfianza;
    
    // Resumen de campos
    private Integer totalCampos;
    private Integer camposExactos;
    private Integer camposBuenos;
    private Integer camposRegulares;
    private Integer camposMalos;
    
    // Metadatos
    private LocalDateTime fechaCreacion;
    private LocalDateTime fechaActualizacion;
    private String estado;
    private String versionApi;
    private Long tiempoProcesamiento;
    private String observaciones;
    
    // Datos detallados (opcionales)
    private Map<String, Object> camposAnalizados;
    private Map<String, Object> estadisticas;
    private Map<String, Object> resumen;
    private String[] recomendaciones;
    
    // Información del cliente
    private String campania;
    private String nifNie;
    private LocalDateTime fechaCreacionCliente;
    
    /**
     * Constructor simplificado para listados
     */
    public TranscriptionAnalysisDTO(Long id, Long clienteResidencialId, String nombreCliente, 
                                   String movilContacto, String numeroAgente, BigDecimal porcentajePromedio, 
                                   String nivelConfianza, LocalDateTime fechaCreacion, String estado) {
        this.id = id;
        this.clienteResidencialId = clienteResidencialId;
        this.nombreCliente = nombreCliente;
        this.movilContacto = movilContacto;
        this.numeroAgente = numeroAgente;
        this.porcentajePromedio = porcentajePromedio;
        this.nivelConfianza = nivelConfianza;
        this.fechaCreacion = fechaCreacion;
        this.estado = estado;
    }
    
    /**
     * Calcula el porcentaje de eficiencia basado en campos exactos y buenos
     */
    public Double getEficienciaPositiva() {
        if (totalCampos == null || totalCampos == 0) {
            return 0.0;
        }
        
        int camposPositivos = (camposExactos != null ? camposExactos : 0) + 
                             (camposBuenos != null ? camposBuenos : 0);
        
        return (camposPositivos * 100.0) / totalCampos;
    }
    
    /**
     * Calcula el porcentaje de campos problemáticos
     */
    public Double getEficienciaNegativa() {
        if (totalCampos == null || totalCampos == 0) {
            return 0.0;
        }
        
        int camposNegativos = (camposRegulares != null ? camposRegulares : 0) + 
                             (camposMalos != null ? camposMalos : 0);
        
        return (camposNegativos * 100.0) / totalCampos;
    }
    
    /**
     * Determina si el análisis es considerado exitoso
     */
    public boolean isAnalisisExitoso() {
        return porcentajePromedio != null && porcentajePromedio.doubleValue() >= 70.0;
    }
    
    /**
     * Determina si el análisis necesita revisión
     */
    public boolean isNecesitaRevision() {
        return porcentajePromedio != null && porcentajePromedio.doubleValue() < 50.0;
    }
    
    /**
     * Obtiene una descripción del nivel de confianza
     */
    public String getDescripcionNivelConfianza() {
        if (nivelConfianza == null) return "No definido";
        
        switch (nivelConfianza.toUpperCase()) {
            case "ALTO":
                return "Análisis muy confiable";
            case "MEDIO":
                return "Análisis moderadamente confiable";
            case "BAJO":
                return "Análisis poco confiable";
            case "MUY_BAJO":
                return "Análisis requiere revisión manual";
            default:
                return "Nivel de confianza desconocido";
        }
    }
    
    /**
     * Obtiene el color recomendado para mostrar el porcentaje
     */
    public String getColorPorcentaje() {
        if (porcentajePromedio == null) return "gray";
        
        double porcentaje = porcentajePromedio.doubleValue();
        
        if (porcentaje >= 80) return "green";
        if (porcentaje >= 60) return "yellow";
        if (porcentaje >= 40) return "orange";
        return "red";
    }
    
    /**
     * Obtiene un resumen textual del análisis
     */
    public String getResumenAnalisis() {
        if (porcentajePromedio == null) {
            return "Análisis sin datos de porcentaje";
        }
        
        double porcentaje = porcentajePromedio.doubleValue();
        String nivel = nivelConfianza != null ? nivelConfianza.toLowerCase() : "desconocido";
        
        if (porcentaje >= 80) {
            return String.format("Excelente coincidencia (%.1f%%) con confianza %s", porcentaje, nivel);
        } else if (porcentaje >= 60) {
            return String.format("Buena coincidencia (%.1f%%) con confianza %s", porcentaje, nivel);
        } else if (porcentaje >= 40) {
            return String.format("Coincidencia regular (%.1f%%) con confianza %s", porcentaje, nivel);
        } else {
            return String.format("Baja coincidencia (%.1f%%) con confianza %s - Requiere revisión", porcentaje, nivel);
        }
    }
}
