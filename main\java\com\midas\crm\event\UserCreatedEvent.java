package com.midas.crm.event;

import com.midas.crm.entity.User;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * Evento que se dispara cuando se crea un nuevo usuario
 */
@Getter
public class UserCreatedEvent extends ApplicationEvent {
    
    private final User user;
    
    public UserCreatedEvent(Object source, User user) {
        super(source);
        this.user = user;
    }
}
