package com.midas.crm.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

@Entity
@Table(name = "progreso_usuarios", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"usuario_id", "leccion_id"})
})
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProgresoUsuario {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_id", nullable = false)
    private User usuario;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "leccion_id", nullable = false)
    private Leccion leccion;

    @Column(nullable = false)
    private Boolean completado = false;

    @Column(name = "segundos_vistos", nullable = false)
    private Integer segundosVistos = 0;

    @Column(name = "porcentaje_completado", nullable = false)
    private Integer porcentajeCompletado = 0;

    @Column(name = "ultima_posicion")
    private Integer ultimaPosicion = 0; // Posición en segundos donde se quedó el usuario

    @CreationTimestamp
    @Column(name = "fecha_creacion", updatable = false)
    private LocalDateTime fechaCreacion;

    @UpdateTimestamp
    @Column(name = "ultima_visualizacion")
    private LocalDateTime ultimaVisualizacion;
}
