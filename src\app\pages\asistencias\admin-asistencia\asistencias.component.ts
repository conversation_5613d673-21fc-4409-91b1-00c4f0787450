import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { Subscription } from 'rxjs';
import { Store } from '@ngrx/store';
import { getUser } from '@app/store/user';
import { AsistenciaAdminService } from '@app/services/asistencia-admin.service';
import Swal from 'sweetalert2';
import moment from 'moment';

interface UsuarioSede {
  id: number;
  nombre: string;
  username: string;
  email: string;
  role: string;
  dni: string;
  telefono: string;
  ultimaAsistencia: string | null;
  estadoAsistencia: string;
}

interface DashboardData {
  sede: {
    id: number;
    nombre: string;
    direccion: string;
    ciudad: string;
  };
  totalUsuarios: number;
  usuariosConEntrada: number;
  usuariosConSalida: number;
  usuariosTrabajando: number;
  breaksActivos: number;
  banosActivos: number;
  promedioHorasTrabajadas: number;
  topUsuarios?: Array<{
    usuario: string;
    tiempoMinutos: number;
    tiempoHoras: string;
    porcentaje: number;
  }>;
  asistenciasPorHora: { [key: string]: number };
  fecha: string;
  fechaConsulta: string;
}

interface AsistenciaAdmin {
  id: number;
  usuarioId: number;
  usuarioNombre: string;
  usuarioUsername: string;
  tipoActividad: string;
  fechaHoraEntrada: string;
  fechaHoraSalida: string | null;
  duracionMinutos: number | null;
  estadoActual: string;
  breakContador: number;
  breakTiempoTotalMinutos: number;
  banoContador: number;
  banoTiempoTotalMinutos: number;
  tiempoNetoTrabajadoMinutos: number;
  porcentajeJornada: number;
  observaciones: string;
}

interface FiltrosAsistencia {
  usuarioId: number | null;
  tipoActividad: string | null;
  fechaInicio: string | null;
  fechaFin: string | null;
  sedeId: number | null;
  page: number;
  size: number;
  sortBy: string;
  sortDirection: string;
}

@Component({
  selector: 'app-admin-asistencias',
  templateUrl: './asistencias.component.html',
  styleUrls: ['./asistencias.component.scss'],
})
export class AsistenciasComponent implements OnInit, OnDestroy {
  // Propiedades del usuario actual
  currentUser: any = null;
  isAdmin: boolean = false;

  // Datos del dashboard
  dashboardData: DashboardData | null = null;
  sedeInfo: any = null;
  usuariosSede: UsuarioSede[] = [];

  // 🔥 PAGINACIÓN USUARIOS
  usuariosPaginacion = {
    currentPage: 0,
    totalPages: 0,
    totalElements: 0,
    size: 20,
    hasNext: false,
    hasPrevious: false,
  };
  usuariosSearch = '';
  loadingUsuariosPaginados = false;

  // Fecha seleccionada
  fechaSeleccionada: string = moment().format('YYYY-MM-DD');

  // Filtros y búsqueda
  filtros: FiltrosAsistencia = {
    usuarioId: null,
    tipoActividad: null,
    fechaInicio: moment().format('YYYY-MM-DD'),
    fechaFin: moment().format('YYYY-MM-DD'),
    sedeId: null,
    page: 0,
    size: 20,
    sortBy: 'fechaHoraEntrada',
    sortDirection: 'DESC',
  };

  // Resultados de la tabla
  asistencias: AsistenciaAdmin[] = [];
  totalRegistros: number = 0;
  totalPaginas: number = 0;
  paginaActual: number = 1;

  // Estados de la UI
  loading: boolean = false;
  loadingDashboard: boolean = false;
  loadingUsuarios: boolean = false;
  loadingExportacion: boolean = false;

  // Suscripciones
  private subscription = new Subscription();

  constructor(
    private store: Store,
    private asistenciaAdminService: AsistenciaAdminService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.initializeComponent();
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  private initializeComponent(): void {
    this.subscription.add(
      this.store.select(getUser).subscribe((user) => {
        if (user) {
          this.currentUser = user;
          this.isAdmin = user.role === 'ADMIN';

          console.log('🔥 Usuario completo recibido del store:', {
            user: user,
            sede: user.sede,
            sedeId: user.sedeId,
            sede_id: user.sede_id,
            isAdmin: this.isAdmin,
          });

          if (this.isAdmin) {
            console.log('🔥 Usuario admin detectado:', user);

            // Configurar sede en filtros - probar múltiples fuentes
            let sedeId = null;
            if (user.sede?.id) {
              sedeId = user.sede.id;
              console.log('✅ Sede encontrada en user.sede.id:', sedeId);
            } else if (user.sedeId) {
              sedeId = user.sedeId;
              console.log('✅ Sede encontrada en user.sedeId:', sedeId);
            } else if (user.sede_id) {
              sedeId = user.sede_id;
              console.log('✅ Sede encontrada en user.sede_id:', sedeId);
            } else {
              console.warn(
                '⚠️ No se encontró sede en ninguna propiedad del usuario'
              );
            }

            if (sedeId) {
              this.filtros.sedeId = sedeId;
              console.log('✅ Sede configurada en filtros:', sedeId);
            } else {
              console.error('❌ No se pudo configurar sede en filtros');
            }

            this.loadInitialData();
          } else {
            this.showUnauthorizedMessage();
          }
        } else {
          console.warn('⚠️ Usuario no encontrado en store');
        }
      })
    );
  }

  private async loadInitialData(): Promise<void> {
    try {
      console.log('🔥 Iniciando carga de datos iniciales...');

      // Cargar en paralelo con manejo de errores independiente
      const promises = [
        this.cargarUsuariosPaginados(0).catch((err) => {
          console.error('❌ Error en usuarios sede paginados:', err);
          return null;
        }),
        this.cargarDashboard().catch((err) => {
          console.error('❌ Error en dashboard:', err);
          return null;
        }),
      ];

      await Promise.all(promises);

      // Cargar asistencias después
      await this.aplicarFiltros().catch((err) => {
        console.error('❌ Error en asistencias:', err);
      });

      console.log('✅ Carga inicial completada');
    } catch (error) {
      console.error('❌ Error en carga inicial:', error);
      this.showErrorMessage('Error al cargar los datos iniciales');
    }
  }

  async cargarUsuariosSede(): Promise<void> {
    try {
      this.loadingUsuarios = true;
      console.log('🏢 Cargando usuarios de sede...');

      const response = await this.asistenciaAdminService
        .obtenerUsuariosSede()
        .toPromise();

      if (response?.rpta === 1) {
        this.usuariosSede = response.data || [];
        console.log('✅ Usuarios de sede cargados:', this.usuariosSede.length);
      } else {
        console.warn('⚠️ Respuesta no exitosa para usuarios sede:', response);
        this.usuariosSede = [];
      }
    } catch (error: any) {
      console.error('❌ Error al cargar usuarios de sede:', error);

      // Mostrar error específico según el código de estado
      let mensaje = 'Error al cargar usuarios de la sede';
      if (error?.status === 401) {
        mensaje = 'Sesión expirada. Inicie sesión nuevamente';
      } else if (error?.status === 403) {
        mensaje = 'Sin permisos para ver usuarios de la sede';
      } else if (error?.status === 400) {
        mensaje = 'Admin sin sede asignada';
      }

      this.showErrorMessage(mensaje);
      this.usuariosSede = [];
    } finally {
      this.loadingUsuarios = false;
    }
  }

  /**
   * 🔥 NUEVO: Cargar usuarios con paginación
   */
  async cargarUsuariosPaginados(
    page: number = 0,
    search?: string
  ): Promise<void> {
    try {
      this.loadingUsuariosPaginados = true;
      console.log('🏢 Cargando usuarios paginados...', { page, search });

      const response = await this.asistenciaAdminService
        .obtenerUsuariosSedePaginado(page, this.usuariosPaginacion.size, search)
        .toPromise();

      if (response?.rpta === 1) {
        const data = response.data;
        console.log('🔥 Datos recibidos del backend:', data);

        this.usuariosSede = data.usuarios || [];
        this.usuariosPaginacion = {
          currentPage: data.currentPage || 0,
          totalPages: data.totalPages || 0,
          totalElements: data.totalElements || 0,
          size: data.size || 20,
          hasNext: data.hasNext || false,
          hasPrevious: data.hasPrevious || false,
        };

        console.log('✅ Usuarios paginados cargados:', {
          usuarios: this.usuariosSede.length,
          usuariosArray: this.usuariosSede,
          paginacion: this.usuariosPaginacion,
          search: search,
        });
      } else {
        console.warn(
          '⚠️ Respuesta no exitosa para usuarios paginados:',
          response
        );
        this.usuariosSede = [];
        this.resetPaginacion();
      }
    } catch (error: any) {
      console.error('❌ Error al cargar usuarios paginados:', error);

      let mensaje = 'Error al cargar usuarios de la sede';
      if (error?.status === 403) {
        mensaje = 'Sin permisos o sede no asignada';
      } else if (error?.status === 401) {
        mensaje = 'Sesión expirada. Por favor, inicie sesión nuevamente';
      } else if (error?.status === 400) {
        mensaje = 'Admin sin sede asignada';
      }

      this.showErrorMessage(mensaje);
      this.usuariosSede = [];
      this.resetPaginacion();
    } finally {
      this.loadingUsuariosPaginados = false;
    }
  }

  /**
   * 🔥 AUXILIAR: Reset paginación
   */
  private resetPaginacion(): void {
    this.usuariosPaginacion = {
      currentPage: 0,
      totalPages: 0,
      totalElements: 0,
      size: 20,
      hasNext: false,
      hasPrevious: false,
    };
  }

  /**
   * 🔥 BÚSQUEDA: Buscar usuarios
   */
  /**  🔍 BÚSQUEDA: Buscar usuarios y refrescar tabla  */
  async onUsuariosSearch(): Promise<void> {
    console.log('🔍 BÚSQUEDA DE USUARIOS - Término:', this.usuariosSearch);
    console.log('📋 API: /api/asistencias/admin/usuarios-sede (paginado)');
    console.log('🎯 Propósito: Filtrar lista de usuarios para selección en dropdown');

    // 1. Cargamos la página 0 con el término de búsqueda
    await this.cargarUsuariosPaginados(0, this.usuariosSearch);

    // 2. Mostrar resultado de la búsqueda
    if (!this.usuariosSearch.trim()) {
      console.log('✅ Búsqueda vacía - Mostrando todos los usuarios');
      this.filtros.usuarioId = null; // → Todos los usuarios
    } else if (this.usuariosSede.length === 0) {
      console.log('❌ Sin resultados para:', this.usuariosSearch);
      this.showErrorMessage(`No se encontraron usuarios con el término: "${this.usuariosSearch}"`);
      return;
    } else if (this.usuariosSede.length === 1) {
      console.log('✅ Usuario único encontrado:', this.usuariosSede[0].nombre);
      this.filtros.usuarioId = this.usuariosSede[0].id; // → Único match
    } else {
      console.log(`✅ ${this.usuariosSede.length} usuarios encontrados - Seleccione uno del dropdown`);
      this.filtros.usuarioId = null; // → Varios matches, esperar selección manual
    }

    // 3. Refrescamos la tabla con los filtros actualizados
    await this.aplicarFiltros();
  }

  /**
   * 🔥 PAGINACIÓN: Ir a página anterior
   */
  irPaginaAnterior(): void {
    if (this.usuariosPaginacion.hasPrevious) {
      this.cargarUsuariosPaginados(
        this.usuariosPaginacion.currentPage - 1,
        this.usuariosSearch
      );
    }
  }

  /**
   * 🔥 PAGINACIÓN: Ir a página siguiente
   */
  irPaginaSiguiente(): void {
    if (this.usuariosPaginacion.hasNext) {
      this.cargarUsuariosPaginados(
        this.usuariosPaginacion.currentPage + 1,
        this.usuariosSearch
      );
    }
  }

  /**
   * 🔥 PAGINACIÓN: Ir a página específica
   */
  irPagina(page: number): void {
    if (page >= 0 && page < this.usuariosPaginacion.totalPages) {
      this.cargarUsuariosPaginados(page, this.usuariosSearch);
    }
  }

  async cargarDashboard(): Promise<void> {
    try {
      this.loadingDashboard = true;
      console.log('📊 Cargando dashboard para fecha:', this.fechaSeleccionada);

      const response = await this.asistenciaAdminService
        .obtenerDashboard(this.fechaSeleccionada)
        .toPromise();

      if (response?.rpta === 1) {
        this.dashboardData = response.data;
        this.sedeInfo = this.dashboardData?.sede;
        console.log('✅ Dashboard cargado:', this.dashboardData);
      } else {
        console.warn('⚠️ Respuesta no exitosa para dashboard:', response);
        this.dashboardData = null;
      }
    } catch (error: any) {
      console.error('❌ Error al cargar dashboard:', error);

      let mensaje = 'Error al cargar el dashboard';
      if (error?.status === 401) {
        mensaje = 'Sesión expirada. Inicie sesión nuevamente';
      } else if (error?.status === 403) {
        mensaje = 'Sin permisos para ver el dashboard';
      }

      this.showErrorMessage(mensaje);
      this.dashboardData = null;
    } finally {
      this.loadingDashboard = false;
    }
  }

  async aplicarFiltros(): Promise<void> {
    try {
      this.loading = true;
      console.log('🔍 APLICANDO FILTROS DE ASISTENCIAS');
      console.log('📋 API: /api/asistencias/admin/filtros');
      console.log('🎯 Propósito: Obtener registros de asistencia filtrados para la tabla');
      console.log('📊 Filtros aplicados:', this.filtros);

      const response = await this.asistenciaAdminService
        .obtenerAsistenciasConFiltros(this.filtros)
        .toPromise();

      if (response?.rpta === 1) {
        const pageData = response.data;
        this.asistencias = pageData.content || [];
        this.totalRegistros = pageData.totalElements || 0;
        this.totalPaginas = pageData.totalPages || 0;
        this.paginaActual = (pageData.number || 0) + 1;

        console.log('✅ ASISTENCIAS CARGADAS EXITOSAMENTE:');
        console.log(`📊 Total registros: ${this.totalRegistros}`);
        console.log(`📄 Página actual: ${this.paginaActual} de ${this.totalPaginas}`);
        console.log(`📋 Registros en página: ${this.asistencias.length}`);
        console.log(`💬 Mensaje del servidor: "${response.msg}"`);
      } else {
        console.warn('⚠️ Respuesta no exitosa para asistencias:', response);
        this.asistencias = [];
        this.totalRegistros = 0;
      }
    } catch (error: any) {
      console.error('❌ Error al aplicar filtros:', error);

      let mensaje = 'Error al cargar las asistencias';
      if (error?.status === 401) {
        mensaje = 'Sesión expirada. Inicie sesión nuevamente';
      } else if (error?.status === 403) {
        mensaje = 'Sin permisos para ver asistencias';
      }

      this.showErrorMessage(mensaje);
      this.asistencias = [];
      this.totalRegistros = 0;
    } finally {
      this.loading = false;
    }
  }

  onFechaChange(): void {
    console.log('📅 Fecha cambiada a:', this.fechaSeleccionada);
    this.filtros.fechaInicio = this.fechaSeleccionada;
    this.filtros.fechaFin = this.fechaSeleccionada;

    // Cargar dashboard y asistencias en paralelo
    Promise.all([this.cargarDashboard(), this.aplicarFiltros()]).catch(
      (error) => {
        console.error('❌ Error al cambiar fecha:', error);
      }
    );
  }

  limpiarFiltros(): void {
    console.log('🧹 Limpiando filtros...');

    // Determinar sedeId de múltiples fuentes
    let sedeId = null;
    if (this.currentUser?.sede?.id) {
      sedeId = this.currentUser.sede.id;
    } else if (this.currentUser?.sedeId) {
      sedeId = this.currentUser.sedeId;
    } else if (this.currentUser?.sede_id) {
      sedeId = this.currentUser.sede_id;
    } else if (this.sedeInfo?.id) {
      sedeId = this.sedeInfo.id;
    }

    console.log('🔧 Sede para filtros limpios:', sedeId);

    this.filtros = {
      usuarioId: null,
      tipoActividad: null,
      fechaInicio: moment().format('YYYY-MM-DD'),
      fechaFin: moment().format('YYYY-MM-DD'),
      sedeId: sedeId,
      page: 0,
      size: 20,
      sortBy: 'fechaHoraEntrada',
      sortDirection: 'DESC',
    };

    this.fechaSeleccionada = moment().format('YYYY-MM-DD');

    console.log('✅ Filtros limpiados:', this.filtros);

    // Recargar datos
    Promise.all([this.cargarDashboard(), this.aplicarFiltros()]).catch(
      (error) => {
        console.error('❌ Error al limpiar filtros:', error);
      }
    );
  }

  cambiarPagina(nuevaPagina: number): void {
    if (nuevaPagina >= 1 && nuevaPagina <= this.totalPaginas) {
      console.log('📄 Cambiando a página:', nuevaPagina);
      this.filtros.page = nuevaPagina - 1;
      this.aplicarFiltros();
    }
  }

  // 🔥 MÉTODO PRINCIPAL DE EXPORTACIÓN CORREGIDO
  async exportarExcel(): Promise<void> {
    try {
      console.log('🔥 INICIO EXPORTACIÓN - Estado actual:', {
        currentUser: this.currentUser,
        sedeInfo: this.sedeInfo,
        filtros: this.filtros,
        totalRegistros: this.totalRegistros,
      });

      if (this.totalRegistros === 0) {
        Swal.fire({
          title: 'Sin Datos',
          text: 'No hay registros para exportar con los filtros actuales',
          icon: 'warning',
          confirmButtonText: 'Entendido',
        });
        return;
      }

      this.loadingExportacion = true;

      // Mostrar loading con información detallada
      Swal.fire({
        title: 'Generando Excel...',
        html: `
          <div class="flex flex-col items-center space-y-3">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
            <p class="font-medium">Exportando ${
              this.totalRegistros
            } registros...</p>
            <p class="text-sm text-gray-600">Sede: ${
              this.sedeInfo?.nombre || 'N/A'
            }</p>
            <p class="text-sm text-gray-500">Fecha: ${
              this.fechaSeleccionada
            }</p>
            <div class="text-xs text-gray-400 mt-2">
              <p>⏳ Esto puede tomar unos momentos...</p>
            </div>
          </div>
        `,
        allowOutsideClick: false,
        showConfirmButton: false,
        customClass: {
          popup: 'rounded-lg',
        },
      });

      // 🔥 PREPARAR FILTROS ESPECÍFICOS PARA EXPORTACIÓN
      const filtrosExportacion = {
        ...this.filtros,
        page: 0,
        size: 10000, // Exportar máximo 10k registros
        sortBy: 'fechaHoraEntrada',
        sortDirection: 'DESC',
      };

      console.log('🔥 FILTROS ANTES DE VALIDACIÓN:', filtrosExportacion);

      // 🔥 VALIDAR Y CORREGIR SEDE
      if (!filtrosExportacion.sedeId && this.currentUser?.sede?.id) {
        console.log(
          '⚠️ SedeId faltante, aplicando desde currentUser:',
          this.currentUser.sede.id
        );
        filtrosExportacion.sedeId = this.currentUser.sede.id;
      }

      if (!filtrosExportacion.sedeId && this.sedeInfo?.id) {
        console.log(
          '⚠️ SedeId faltante, aplicando desde sedeInfo:',
          this.sedeInfo.id
        );
        filtrosExportacion.sedeId = this.sedeInfo.id;
      }

      // 🔥 VALIDAR FILTROS ANTES DE ENVIAR
      if (!filtrosExportacion.fechaInicio || !filtrosExportacion.fechaFin) {
        throw new Error('Fechas de filtro requeridas para exportación');
      }

      if (!filtrosExportacion.sedeId) {
        console.error('❌ SEDE FALTANTE - Estado completo:', {
          currentUser: this.currentUser,
          sedeInfo: this.sedeInfo,
          filtros: this.filtros,
          filtrosExportacion: filtrosExportacion,
        });
        throw new Error('Sede requerida para exportación');
      }

      console.log('📊 Exportando con filtros validados:', filtrosExportacion);

      // Realizar exportación
      const response = await this.asistenciaAdminService
        .exportarAsistenciasExcel(filtrosExportacion)
        .toPromise();

      if (!response || !(response instanceof Blob)) {
        throw new Error('Respuesta inválida del servidor - no es un Blob');
      }

      if (response.size === 0) {
        throw new Error('El archivo generado está vacío');
      }

      // Generar nombre y descargar
      const fecha = moment().format('YYYY-MM-DD_HH-mm');
      const sede =
        this.sedeInfo?.nombre?.replace(/[^a-zA-Z0-9]/g, '_') || 'general';
      const rangoFecha =
        this.filtros.fechaInicio === this.filtros.fechaFin
          ? moment(this.filtros.fechaInicio).format('DD-MM-YYYY')
          : `${moment(this.filtros.fechaInicio).format('DD-MM')}_${moment(
              this.filtros.fechaFin
            ).format('DD-MM-YYYY')}`;

      const filename = `Asistencias_${sede}_${rangoFecha}_${fecha}.xlsx`;

      this.descargarArchivo(response, filename);

      Swal.close();

      // Mostrar éxito con detalles
      Swal.fire({
        title: '¡Descarga Completa!',
        html: `
          <div class="text-center space-y-3">
            <div class="text-green-500 text-5xl mb-4">
              <i class="fas fa-file-excel"></i>
            </div>
            <p class="font-medium text-lg">Archivo descargado correctamente</p>
            <div class="bg-gray-50 p-3 rounded-lg text-sm">
              <p class="font-medium text-gray-700">📁 ${filename}</p>
              <p class="text-gray-600">📊 ${this.totalRegistros} registros exportados</p>
              <p class="text-gray-600">🏢 ${this.sedeInfo?.nombre}</p>
              <p class="text-gray-600">📅 ${rangoFecha}</p>
            </div>
            <p class="text-xs text-gray-500 mt-2">
              El archivo se ha guardado en su carpeta de descargas
            </p>
          </div>
        `,
        icon: 'success',
        timer: 6000,
        timerProgressBar: true,
        confirmButtonText: 'Entendido',
        confirmButtonColor: '#10B981',
      });
    } catch (error: any) {
      console.error('❌ Error al exportar:', error);

      Swal.close();

      let errorMessage = 'Error al generar el archivo Excel';
      let errorDetails = '';

      if (error?.status === 500) {
        errorMessage = 'Error del servidor al generar el archivo';
        errorDetails =
          'Intente reducir el rango de fechas o contacte al administrador';
      } else if (error?.status === 401) {
        errorMessage = 'Sesión expirada';
        errorDetails = 'Inicie sesión nuevamente e intente de nuevo';
      } else if (error?.status === 403) {
        errorMessage = 'Sin permisos para exportar';
        errorDetails = 'Contacte al administrador del sistema';
      } else if (error?.status === 400) {
        errorMessage = 'Datos de exportación inválidos';
        errorDetails = 'Verifique los filtros aplicados';
      } else if (error?.message) {
        errorMessage = error.message;
      }

      Swal.fire({
        title: 'Error de Exportación',
        html: `
          <div class="text-center space-y-3">
            <div class="text-red-500 text-4xl mb-3">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <p class="font-medium">${errorMessage}</p>
            ${
              errorDetails
                ? `<p class="text-sm text-gray-600">${errorDetails}</p>`
                : ''
            }
            <div class="text-xs text-gray-500 mt-3">
              <p>Código: ${error?.status || 'UNKNOWN'}</p>
            </div>
          </div>
        `,
        icon: 'error',
        confirmButtonText: 'Entendido',
        confirmButtonColor: '#EF4444',
      });
    } finally {
      this.loadingExportacion = false;
    }
  }

  // 🔥 MÉTODO AUXILIAR PARA DESCARGAR ARCHIVO MEJORADO
  private descargarArchivo(blob: Blob, filename: string): void {
    try {
      // Verificar que el blob tenga contenido
      if (!blob || blob.size === 0) {
        throw new Error('Archivo vacío recibido');
      }

      // Crear URL temporal
      const url = window.URL.createObjectURL(blob);

      // Crear elemento de descarga
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.style.display = 'none';

      // Añadir al DOM, hacer clic y remover
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Limpiar URL después de un delay
      setTimeout(() => {
        window.URL.revokeObjectURL(url);
      }, 1000);

      console.log(
        '✅ Archivo descargado exitosamente:',
        filename,
        `(${blob.size} bytes)`
      );
    } catch (error) {
      console.error('❌ Error al descargar archivo:', error);
      throw new Error(
        'No se pudo descargar el archivo: ' + (error as Error).message
      );
    }
  }

  // 🔥 MÉTODOS DE PAGINACIÓN
  getStartRecord(): number {
    return Math.max(1, (this.paginaActual - 1) * this.filtros.size + 1);
  }

  getEndRecord(): number {
    return Math.min(this.paginaActual * this.filtros.size, this.totalRegistros);
  }

  // 🔥 MÉTODOS DE FORMATEO PARA EL TEMPLATE
  getTipoActividadLabel(tipo: string): string {
    const labels: { [key: string]: string } = {
      ENTRADA: 'Entrada',
      SALIDA: 'Salida',
      BREAK: 'Break',
      BANO: 'Baño',
      SESION_CRM: 'Sesión CRM',
    };
    return labels[tipo] || tipo;
  }

  getEstadoLabel(estado: string): string {
    const labels: { [key: string]: string } = {
      TRABAJANDO: 'Trabajando',
      EN_BREAK: 'En Break',
      EN_BANO: 'En Baño',
      SALIDA: 'Salida',
      SIN_REGISTRO: 'Sin Registro',
    };
    return labels[estado] || estado;
  }

  formatearMinutos(minutos: number): string {
    if (!minutos || minutos === 0) return '0m';

    const horas = Math.floor(minutos / 60);
    const mins = minutos % 60;

    if (horas > 0) {
      return mins > 0 ? `${horas}h ${mins}m` : `${horas}h`;
    } else {
      return `${mins}m`;
    }
  }

  getUsuarioInfo(usuarioId: number): UsuarioSede | null {
    return this.usuariosSede.find((u) => u.id === usuarioId) || null;
  }

  // 🔥 MÉTODOS PARA TOP USUARIOS
  getTopUsuarios(): Array<{
    usuario: string;
    tiempoMinutos: number;
    tiempoHoras: string;
    porcentaje: number;
  }> {
    return this.dashboardData?.topUsuarios || [];
  }

  hasTopUsuarios(): boolean {
    return (
      this.dashboardData?.topUsuarios != null &&
      this.dashboardData.topUsuarios.length > 0
    );
  }

  // 🔥 MÉTODOS DE NOTIFICACIONES MEJORADOS
  private showErrorMessage(message: string, details?: string): void {
    Swal.fire({
      title: '¡Error!',
      html: `
        <div class="text-center space-y-2">
          <p>${message}</p>
          ${details ? `<p class="text-sm text-gray-600">${details}</p>` : ''}
        </div>
      `,
      icon: 'error',
      confirmButtonText: 'Entendido',
      confirmButtonColor: '#dc3545',
    });
  }

  private showSuccessMessage(title: string, text?: string): void {
    Swal.fire({
      title: title,
      text: text || '',
      icon: 'success',
      confirmButtonText: 'Entendido',
      confirmButtonColor: '#28a745',
      timer: text ? undefined : 3000,
      timerProgressBar: !text,
    });
  }

  private showUnauthorizedMessage(): void {
    Swal.fire({
      title: 'Acceso Denegado',
      text: 'No tiene permisos de administrador para ver esta página',
      icon: 'warning',
      confirmButtonText: 'Entendido',
      confirmButtonColor: '#ffc107',
    }).then(() => {
      window.history.back();
    });
  }

  // 🔥 MÉTODOS ADICIONALES DE ANÁLISIS
  calcularPorcentajeAsistencia(): number {
    if (!this.dashboardData) return 0;

    const { totalUsuarios, usuariosConEntrada } = this.dashboardData;
    return totalUsuarios > 0 ? (usuariosConEntrada / totalUsuarios) * 100 : 0;
  }

  obtenerColorEstado(estado: string): string {
    const colores: { [key: string]: string } = {
      TRABAJANDO: 'text-green-600',
      EN_BREAK: 'text-yellow-600',
      EN_BANO: 'text-blue-600',
      SALIDA: 'text-gray-600',
      SIN_REGISTRO: 'text-red-600',
    };
    return colores[estado] || 'text-gray-600';
  }

  obtenerIconoEstado(estado: string): string {
    const iconos: { [key: string]: string } = {
      TRABAJANDO: 'fas fa-briefcase',
      EN_BREAK: 'fas fa-coffee',
      EN_BANO: 'fas fa-restroom',
      SALIDA: 'fas fa-sign-out-alt',
      SIN_REGISTRO: 'fas fa-exclamation-triangle',
    };
    return iconos[estado] || 'fas fa-question-circle';
  }

  // 🔥 MÉTODOS DE FILTROS RÁPIDOS
  filtrarPorHoy(): void {
    const hoy = moment().format('YYYY-MM-DD');
    this.fechaSeleccionada = hoy;
    this.filtros.fechaInicio = hoy;
    this.filtros.fechaFin = hoy;
    this.onFechaChange();
  }

  filtrarPorSemana(): void {
    const inicioSemana = moment().startOf('week').format('YYYY-MM-DD');
    const finSemana = moment().endOf('week').format('YYYY-MM-DD');

    this.filtros.fechaInicio = inicioSemana;
    this.filtros.fechaFin = finSemana;
    this.aplicarFiltros();
  }

  filtrarPorMes(): void {
    const inicioMes = moment().startOf('month').format('YYYY-MM-DD');
    const finMes = moment().endOf('month').format('YYYY-MM-DD');

    this.filtros.fechaInicio = inicioMes;
    this.filtros.fechaFin = finMes;
    this.aplicarFiltros();
  }

  // 🔥 MÉTODO PARA VER DETALLES DE USUARIO
  verDetalleUsuario(usuarioId: number): void {
    const usuario = this.getUsuarioInfo(usuarioId);
    if (!usuario) return;

    Swal.fire({
      title: `Detalles de ${usuario.nombre}`,
      html: `
        <div class="text-left space-y-3 p-4">
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p class="font-semibold text-gray-700">Información Personal</p>
              <p><strong>Username:</strong> ${usuario.username}</p>
              <p><strong>Email:</strong> ${usuario.email || 'N/A'}</p>
              <p><strong>DNI:</strong> ${usuario.dni}</p>
              <p><strong>Teléfono:</strong> ${usuario.telefono || 'N/A'}</p>
            </div>
            <div>
              <p class="font-semibold text-gray-700">Estado Laboral</p>
              <p><strong>Rol:</strong> ${usuario.role}</p>
              <p><strong>Estado:</strong> 
                <span class="${this.obtenerColorEstado(
                  usuario.estadoAsistencia
                )}">
                  ${this.getEstadoLabel(usuario.estadoAsistencia)}
                </span>
              </p>
              ${
                usuario.ultimaAsistencia
                  ? `<p><strong>Última Asistencia:</strong><br/>${moment(
                      usuario.ultimaAsistencia
                    ).format('DD/MM/YYYY HH:mm')}</p>`
                  : '<p><strong>Última Asistencia:</strong><br/>Sin registro</p>'
              }
            </div>
          </div>
        </div>
      `,
      icon: 'info',
      confirmButtonText: 'Cerrar',
      width: '600px',
      customClass: {
        popup: 'rounded-lg',
      },
    });
  }

  // 🔥 MÉTODO PARA MOSTRAR ESTADÍSTICAS DETALLADAS
  mostrarEstadisticasDetalladas(): void {
    if (!this.dashboardData) return;

    const porcentajeAsistencia = this.calcularPorcentajeAsistencia();

    Swal.fire({
      title: `Estadísticas Detalladas`,
      html: `
        <div class="text-left space-y-4 p-4">
          <div class="text-center mb-4">
            <h3 class="font-bold text-lg text-blue-600">${
              this.sedeInfo?.nombre
            }</h3>
            <p class="text-sm text-gray-600">${
              this.sedeInfo?.ciudad
            } - ${moment(this.fechaSeleccionada).format('DD/MM/YYYY')}</p>
          </div>
          
          <div class="grid grid-cols-2 gap-6 text-sm">
            <div class="space-y-2">
              <p class="font-semibold text-gray-700 border-b pb-1">📊 Asistencia General</p>
              <p>• Total empleados: <span class="font-medium">${
                this.dashboardData.totalUsuarios
              }</span></p>
              <p>• Con entrada: <span class="font-medium text-green-600">${
                this.dashboardData.usuariosConEntrada
              }</span></p>
              <p>• % Asistencia: <span class="font-medium text-blue-600">${porcentajeAsistencia.toFixed(
                1
              )}%</span></p>
              <p>• Con salida: <span class="font-medium text-red-600">${
                this.dashboardData.usuariosConSalida
              }</span></p>
            </div>
            
            <div class="space-y-2">
              <p class="font-semibold text-gray-700 border-b pb-1">⚡ Estado Actual</p>
              <p>• Trabajando: <span class="font-medium text-blue-600">${
                this.dashboardData.usuariosTrabajando
              }</span></p>
              <p>• En break: <span class="font-medium text-yellow-600">${
                this.dashboardData.breaksActivos
              }</span></p>
              <p>• En baño: <span class="font-medium text-cyan-600">${
                this.dashboardData.banosActivos
              }</span></p>
              <p>• Promedio horas: <span class="font-medium text-purple-600">${this.dashboardData.promedioHorasTrabajadas.toFixed(
                1
              )}h</span></p>
            </div>
          </div>
          
          <div class="mt-4 p-3 bg-gray-50 rounded-lg">
            <p class="font-semibold text-gray-700 mb-2">📈 Resumen de Productividad</p>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600">${this.dashboardData.promedioHorasTrabajadas.toFixed(
                1
              )}h</div>
              <div class="text-sm text-gray-600">Promedio de horas trabajadas</div>
            </div>
          </div>
        </div>
      `,
      icon: 'info',
      confirmButtonText: 'Cerrar',
      width: '700px',
      customClass: {
        popup: 'rounded-lg',
      },
    });
  }

  // 🔥 MÉTODOS ADICIONALES DE UTILIDAD
  obtenerResumenDia(): string {
    if (!this.dashboardData) return 'Sin datos';

    const { usuariosConEntrada, totalUsuarios, usuariosTrabajando } =
      this.dashboardData;
    return `${usuariosConEntrada}/${totalUsuarios} presentes (${usuariosTrabajando} trabajando)`;
  }

  hayActividadesActivas(): boolean {
    if (!this.dashboardData) return false;
    return (
      this.dashboardData.breaksActivos > 0 ||
      this.dashboardData.banosActivos > 0
    );
  }

  obtenerMensajeActividades(): string {
    if (!this.dashboardData) return '';

    const { breaksActivos, banosActivos } = this.dashboardData;

    if (breaksActivos > 0 && banosActivos > 0) {
      return `${breaksActivos} breaks y ${banosActivos} baños activos`;
    } else if (breaksActivos > 0) {
      return `${breaksActivos} break${breaksActivos > 1 ? 's' : ''} activo${
        breaksActivos > 1 ? 's' : ''
      }`;
    } else if (banosActivos > 0) {
      return `${banosActivos} baño${banosActivos > 1 ? 's' : ''} activo${
        banosActivos > 1 ? 's' : ''
      }`;
    }

    return 'Sin actividades activas';
  }

  // 🔥 MÉTODO PARA BUSCAR USUARIOS
  buscarUsuario(termino: string): void {
    if (!termino.trim()) {
      this.filtros.usuarioId = null;
    } else {
      const usuario = this.usuariosSede.find(
        (u) =>
          u.nombre.toLowerCase().includes(termino.toLowerCase()) ||
          u.username.toLowerCase().includes(termino.toLowerCase()) ||
          u.dni.includes(termino)
      );

      if (usuario) {
        this.filtros.usuarioId = usuario.id;
      } else {
        this.showErrorMessage(`No se encontró usuario con término: ${termino}`);
        return;
      }
    }

    this.aplicarFiltros();
  }

  // 🔥 MÉTODO PARA DETECTAR FILTROS APLICADOS
  tieneFiltrosAplicados(): boolean {
    return !!(
      this.filtros.usuarioId ||
      this.filtros.tipoActividad ||
      (this.filtros.fechaInicio &&
        this.filtros.fechaInicio !== moment().format('YYYY-MM-DD')) ||
      (this.filtros.fechaFin &&
        this.filtros.fechaFin !== moment().format('YYYY-MM-DD'))
    );
  }

  // 🔥 MÉTODO PARA OBTENER RESUMEN DE FILTROS
  getResumenFiltros(): string {
    const filtrosActivos = [];

    if (this.filtros.usuarioId) {
      const usuario = this.getUsuarioInfo(this.filtros.usuarioId);
      filtrosActivos.push(`Usuario: ${usuario?.nombre || 'Desconocido'}`);
    }

    if (this.filtros.tipoActividad) {
      filtrosActivos.push(
        `Actividad: ${this.getTipoActividadLabel(this.filtros.tipoActividad)}`
      );
    }

    if (this.filtros.fechaInicio && this.filtros.fechaFin) {
      if (this.filtros.fechaInicio === this.filtros.fechaFin) {
        filtrosActivos.push(
          `Fecha: ${moment(this.filtros.fechaInicio).format('DD/MM/YYYY')}`
        );
      } else {
        filtrosActivos.push(
          `Fechas: ${moment(this.filtros.fechaInicio).format(
            'DD/MM'
          )} - ${moment(this.filtros.fechaFin).format('DD/MM/YYYY')}`
        );
      }
    }

    return filtrosActivos.join(' | ');
  }

  // 🔥 MÉTODO PARA REFRESCAR DATOS
  refrescarDatos(): void {
    console.log('🔄 Refrescando todos los datos...');

    this.loadInitialData()
      .then(() => {
        this.showSuccessMessage(
          'Datos actualizados',
          'La información se ha refrescado correctamente'
        );
      })
      .catch((error) => {
        console.error('❌ Error al refrescar:', error);
        this.showErrorMessage('Error al refrescar los datos');
      });
  }

  // 🔥 MÉTODOS DE ESTADO DE CARGA
  isLoadingAny(): boolean {
    return (
      this.loading ||
      this.loadingDashboard ||
      this.loadingUsuarios ||
      this.loadingExportacion
    );
  }

  getEstadoCarga(): string {
    if (this.loadingExportacion) return 'Exportando...';
    if (this.loadingDashboard) return 'Cargando dashboard...';
    if (this.loadingUsuarios) return 'Cargando usuarios...';
    if (this.loading) return 'Cargando asistencias...';
    return '';
  }
}
